# Markitdown工具集 - 快速开始指南

## 🚀 一键启动

双击运行：
```
启动Markitdown工具.command
```

这是唯一需要的启动文件，包含了所有功能的统一入口。

## 📋 功能菜单

启动后会显示功能选择菜单：

### 1. Markitdown转换器 (主要功能)
- **功能**：多格式文档转换为Markdown
- **支持格式**：PDF、Word、Excel、PowerPoint、图片等
- **特色**：集成Open-PDF2MD增强功能，AI智能分析

### 2. PDF转Word工具
- **功能**：专门的PDF转Word转换
- **特色**：多引擎支持，AI结构分析，高质量转换
- **输出**：保持原始格式的Word文档

### 3. Markdown断句修复工具
- **功能**：修复Markdown文档中的断句问题
- **特色**：AI驱动的智能修复，支持多种修复策略
- **适用**：PDF转换后的Markdown文档优化

### 4. 测试增强功能
- **功能**：验证所有增强功能是否正常工作
- **用途**：故障排除，功能验证
- **输出**：详细的测试报告

### 5. 安装/更新依赖
- **功能**：自动安装或更新所需的依赖库
- **包含**：Open-PDF2MD、OCR引擎、AI分析等
- **平台**：支持macOS和Linux

## 🔧 系统要求

### 基础要求
- **操作系统**：macOS 10.14+ 或 Linux
- **Python**：3.7+ (推荐3.9+)
- **内存**：至少4GB RAM
- **存储**：至少2GB可用空间

### 推荐配置
- **Python环境**：Miniconda或Anaconda
- **内存**：8GB+ RAM (用于大文件处理)
- **处理器**：多核CPU (提高处理速度)

## 📦 自动安装

启动器会自动：
1. 检测Python环境
2. 安装基础依赖
3. 安装增强功能依赖
4. 配置OCR引擎
5. 验证安装状态

无需手动安装任何依赖！

## 🎯 使用建议

### 首次使用
1. 双击启动 `启动Markitdown工具.command`
2. 等待自动安装完成
3. 选择功能4测试增强功能
4. 确认所有功能正常后开始使用

### 日常使用
- **文档转换**：选择功能1，使用主转换器
- **PDF转Word**：选择功能2，专门处理PDF
- **文档优化**：选择功能3，修复转换后的文档

### 故障排除
- **依赖问题**：选择功能5重新安装依赖
- **功能异常**：选择功能4运行测试诊断
- **性能问题**：关闭其他应用，释放内存

## 🌟 增强功能

### Open-PDF2MD集成
- **专业PDF处理**：表格、公式、复杂布局
- **高识别率**：中文识别率90-95%
- **多引擎支持**：自动选择最佳处理方法

### AI智能分析
- **结构分析**：自动识别文档结构
- **格式优化**：智能保持原始格式
- **质量提升**：AI驱动的后处理优化

### 多模态支持
- **文本+图像**：结合视觉和文本分析
- **本地处理**：使用本地AI模型，保护隐私
- **实时反馈**：详细的处理状态显示

## 📁 文件结构

```
markitdown/
├── 启动Markitdown工具.command     # 统一启动器 (唯一需要的)
├── markitdown_converter.py        # 主转换器
├── pdf_to_word_gui.py             # PDF转Word工具
├── fix_markdown_sentences.py      # 断句修复工具
├── enhanced_markitdown.py         # 增强转换引擎
├── open_pdf2md_engine.py          # Open-PDF2MD引擎
├── install_enhanced_dependencies.py # 依赖安装器
├── test_enhanced_features.py      # 功能测试
├── ENHANCED_FEATURES.md           # 详细功能说明
└── 快速开始.md                    # 本文档
```

## 🆘 常见问题

### Q: 启动器无法运行？
A: 确保文件有执行权限：`chmod +x 启动Markitdown工具.command`

### Q: Python环境问题？
A: 推荐安装Miniconda，或确保系统有Python 3.7+

### Q: 依赖安装失败？
A: 检查网络连接，或手动运行：`python install_enhanced_dependencies.py`

### Q: OCR功能不可用？
A: 启动器会自动安装Tesseract，如失败请手动安装：`brew install tesseract`

### Q: 转换质量不理想？
A: 尝试不同的转换引擎，或启用AI分析功能

## 📞 技术支持

如果遇到问题：
1. 运行功能测试（选项4）获取诊断信息
2. 查看详细功能说明：`ENHANCED_FEATURES.md`
3. 检查日志输出获取错误详情

## 🔄 更新说明

- **v2.0**：集成Open-PDF2MD，统一启动器
- **v1.x**：基础Markitdown功能

---

**享受强大的文档转换体验！** 🎉
