#!/bin/bash

# Markitdown工具统一启动器
# 集成所有功能的统一入口

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示欢迎信息
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}   Markitdown 工具集 v2.0       ${NC}"
echo -e "${BLUE}   统一启动器 (增强版)           ${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# 检查Python环境
check_python() {
    echo -e "${YELLOW}检查Python环境...${NC}"

    # 优先使用Miniconda/Anaconda
    if command -v conda &> /dev/null; then
        echo -e "${GREEN}使用Conda环境...${NC}"

        # 激活base环境
        source "$(conda info --base)/etc/profile.d/conda.sh"
        conda activate base

        PYTHON_CMD="python"
        PIP_CMD="pip"

    # 检查是否有虚拟环境
    elif [ -d "markitdown_env" ]; then
        echo -e "${GREEN}使用虚拟环境...${NC}"
        source markitdown_env/bin/activate
        PYTHON_CMD="python"
        PIP_CMD="pip"

    # 使用系统Python
    elif command -v python3 &> /dev/null; then
        echo -e "${GREEN}使用系统Python3...${NC}"
        PYTHON_CMD="python3"
        PIP_CMD="pip3"

    else
        echo -e "${RED}错误: 未找到Python环境${NC}"
        echo "请安装Python 3.7+或Miniconda"
        exit 1
    fi

    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    echo -e "${GREEN}Python版本: $PYTHON_VERSION${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}检查和安装依赖...${NC}"

    # 基础依赖
    BASIC_DEPS=("markitdown" "requests" "tkinter")

    for dep in "${BASIC_DEPS[@]}"; do
        if ! $PYTHON_CMD -c "import $dep" 2>/dev/null; then
            echo -e "${YELLOW}正在安装 $dep...${NC}"
            if [ "$dep" = "tkinter" ]; then
                # tkinter通常是Python内置的，如果没有则跳过
                continue
            else
                $PIP_CMD install $dep
            fi
        fi
    done

    # 检查增强功能依赖
    if ! $PYTHON_CMD -c "import pdfplumber" 2>/dev/null; then
        echo -e "${YELLOW}检测到缺少增强功能依赖，正在安装...${NC}"
        $PYTHON_CMD install_enhanced_dependencies.py
    fi
}

# 检查图形界面启动器
check_launcher() {
    if [ ! -f "markitdown_launcher.py" ]; then
        echo -e "${RED}错误: 未找到图形界面启动器${NC}"
        echo "请确保 markitdown_launcher.py 文件存在"
        exit 1
    fi
}

# 主函数
main() {
    # 检查Python环境
    check_python

    # 检查图形界面启动器
    check_launcher

    # 安装依赖
    install_dependencies

    # 显示状态
    echo ""
    echo -e "${GREEN}✅ 环境检查完成${NC}"
    echo -e "${GREEN}✅ 依赖安装完成${NC}"

    # 启动图形界面
    echo -e "${GREEN}启动图形界面...${NC}"
    $PYTHON_CMD markitdown_launcher.py
}

# 错误处理
trap 'echo -e "\n${RED}程序被中断${NC}"; exit 1' INT

# 运行主函数
main
