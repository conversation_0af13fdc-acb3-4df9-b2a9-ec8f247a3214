#!/bin/bash

# Markitdown工具统一启动器
# 集成所有功能的统一入口

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示欢迎信息
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}   Markitdown 工具集 v2.0       ${NC}"
echo -e "${BLUE}   统一启动器 (增强版)           ${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# 检查Python环境
check_python() {
    echo -e "${YELLOW}检查Python环境...${NC}"
    
    # 优先使用Miniconda/Anaconda
    if command -v conda &> /dev/null; then
        echo -e "${GREEN}使用Conda环境...${NC}"
        
        # 激活base环境
        source "$(conda info --base)/etc/profile.d/conda.sh"
        conda activate base
        
        PYTHON_CMD="python"
        PIP_CMD="pip"
        
    # 检查是否有虚拟环境
    elif [ -d "markitdown_env" ]; then
        echo -e "${GREEN}使用虚拟环境...${NC}"
        source markitdown_env/bin/activate
        PYTHON_CMD="python"
        PIP_CMD="pip"
        
    # 使用系统Python
    elif command -v python3 &> /dev/null; then
        echo -e "${GREEN}使用系统Python3...${NC}"
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
        
    else
        echo -e "${RED}错误: 未找到Python环境${NC}"
        echo "请安装Python 3.7+或Miniconda"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    echo -e "${GREEN}Python版本: $PYTHON_VERSION${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}检查和安装依赖...${NC}"
    
    # 基础依赖
    BASIC_DEPS=("markitdown" "requests" "tkinter")
    
    for dep in "${BASIC_DEPS[@]}"; do
        if ! $PYTHON_CMD -c "import $dep" 2>/dev/null; then
            echo -e "${YELLOW}正在安装 $dep...${NC}"
            if [ "$dep" = "tkinter" ]; then
                # tkinter通常是Python内置的，如果没有则跳过
                continue
            else
                $PIP_CMD install $dep
            fi
        fi
    done
    
    # 检查增强功能依赖
    if ! $PYTHON_CMD -c "import pdfplumber" 2>/dev/null; then
        echo -e "${YELLOW}检测到缺少增强功能依赖，正在安装...${NC}"
        $PYTHON_CMD install_enhanced_dependencies.py
    fi
}

# 显示功能菜单
show_menu() {
    echo ""
    echo -e "${BLUE}请选择要启动的功能:${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} Markitdown转换器 (主要功能)"
    echo -e "${GREEN}2.${NC} PDF转Word工具"
    echo -e "${GREEN}3.${NC} Markdown断句修复工具"
    echo -e "${GREEN}4.${NC} 测试增强功能"
    echo -e "${GREEN}5.${NC} 安装/更新依赖"
    echo ""
    echo -e "${YELLOW}0.${NC} 退出"
    echo ""
    echo -n -e "${BLUE}请输入选项 (1-5, 0退出): ${NC}"
}

# 启动Markitdown转换器
start_markitdown_converter() {
    echo -e "${GREEN}启动Markitdown转换器...${NC}"
    
    if [ -f "markitdown_converter.py" ]; then
        $PYTHON_CMD markitdown_converter.py
    else
        echo -e "${RED}错误: 未找到markitdown_converter.py${NC}"
        return 1
    fi
}

# 启动PDF转Word工具
start_pdf_to_word() {
    echo -e "${GREEN}启动PDF转Word工具...${NC}"
    
    if [ -f "pdf_to_word_gui.py" ]; then
        $PYTHON_CMD pdf_to_word_gui.py
    else
        echo -e "${RED}错误: 未找到pdf_to_word_gui.py${NC}"
        return 1
    fi
}

# 启动断句修复工具
start_sentence_fixer() {
    echo -e "${GREEN}启动Markdown断句修复工具...${NC}"
    
    if [ -f "fix_markdown_sentences.py" ]; then
        $PYTHON_CMD fix_markdown_sentences.py
    else
        echo -e "${RED}错误: 未找到fix_markdown_sentences.py${NC}"
        return 1
    fi
}

# 测试增强功能
test_enhanced_features() {
    echo -e "${GREEN}测试增强功能...${NC}"
    
    if [ -f "test_enhanced_features.py" ]; then
        $PYTHON_CMD test_enhanced_features.py
    else
        echo -e "${RED}错误: 未找到test_enhanced_features.py${NC}"
        return 1
    fi
}

# 安装/更新依赖
install_update_dependencies() {
    echo -e "${GREEN}安装/更新依赖...${NC}"
    
    if [ -f "install_enhanced_dependencies.py" ]; then
        $PYTHON_CMD install_enhanced_dependencies.py
    else
        echo -e "${RED}错误: 未找到install_enhanced_dependencies.py${NC}"
        return 1
    fi
}

# 主循环
main_loop() {
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                start_markitdown_converter
                ;;
            2)
                start_pdf_to_word
                ;;
            3)
                start_sentence_fixer
                ;;
            4)
                test_enhanced_features
                ;;
            5)
                install_update_dependencies
                ;;
            0)
                echo -e "${GREEN}感谢使用Markitdown工具集！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选项，请重新选择${NC}"
                ;;
        esac
        
        echo ""
        echo -e "${YELLOW}按回车键继续...${NC}"
        read -r
    done
}

# 主函数
main() {
    # 检查Python环境
    check_python
    
    # 安装依赖
    install_dependencies
    
    # 显示状态
    echo ""
    echo -e "${GREEN}✅ 环境检查完成${NC}"
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
    
    # 进入主循环
    main_loop
}

# 错误处理
trap 'echo -e "\n${RED}程序被中断${NC}"; exit 1' INT

# 运行主函数
main

# 保持终端打开
echo ""
echo -e "${YELLOW}按任意键关闭窗口...${NC}"
read -n 1
