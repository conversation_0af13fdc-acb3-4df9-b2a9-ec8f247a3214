#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enhanced Features Test - 增强功能测试脚本
验证Open-PDF2MD功能集成是否成功
"""

import os
import sys
import time
import tempfile
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('enhanced_test')

class EnhancedFeaturesTest:
    """增强功能测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.temp_dir = tempfile.mkdtemp()
        
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        self.test_results[test_name] = {
            'success': success,
            'message': message,
            'timestamp': time.time()
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")
    
    def test_dependency_imports(self):
        """测试依赖导入"""
        logger.info("测试依赖导入...")
        
        # 测试基础依赖
        basic_deps = {
            'markitdown': 'markitdown',
            'requests': 'requests',
            'numpy': 'numpy',
        }
        
        for package, import_name in basic_deps.items():
            try:
                __import__(import_name)
                self.log_test_result(f"导入{package}", True, f"{package}导入成功")
            except ImportError as e:
                self.log_test_result(f"导入{package}", False, f"{package}导入失败: {str(e)}")
        
        # 测试PDF处理依赖
        pdf_deps = {
            'pdfplumber': 'pdfplumber',
            'PyMuPDF': 'fitz',
            'python-docx': 'docx',
        }
        
        for package, import_name in pdf_deps.items():
            try:
                __import__(import_name)
                self.log_test_result(f"导入{package}", True, f"{package}导入成功")
            except ImportError as e:
                self.log_test_result(f"导入{package}", False, f"{package}导入失败: {str(e)}")
        
        # 测试可选依赖
        optional_deps = {
            'paddleocr': 'paddleocr',
            'pytesseract': 'pytesseract',
        }
        
        for package, import_name in optional_deps.items():
            try:
                __import__(import_name)
                self.log_test_result(f"导入{package}(可选)", True, f"{package}导入成功")
            except ImportError:
                self.log_test_result(f"导入{package}(可选)", True, f"{package}未安装(可选)")
    
    def test_enhanced_converter_init(self):
        """测试增强转换器初始化"""
        logger.info("测试增强转换器初始化...")
        
        try:
            from enhanced_markitdown import EnhancedMarkitdownConverter
            converter = EnhancedMarkitdownConverter()
            
            # 检查引擎状态
            engine_status = converter.get_engine_status()
            
            if engine_status['markitdown']:
                self.log_test_result("Markitdown引擎", True, "Markitdown引擎初始化成功")
            else:
                self.log_test_result("Markitdown引擎", False, "Markitdown引擎初始化失败")
            
            if engine_status['open_pdf2md']:
                self.log_test_result("Open-PDF2MD引擎", True, "Open-PDF2MD引擎初始化成功")
            else:
                self.log_test_result("Open-PDF2MD引擎", False, "Open-PDF2MD引擎初始化失败")
            
            # 检查支持的格式
            supported_formats = converter.get_supported_formats()
            if '.pdf' in supported_formats:
                self.log_test_result("PDF格式支持", True, f"支持{len(supported_formats)}种格式")
            else:
                self.log_test_result("PDF格式支持", False, "PDF格式不支持")
                
        except ImportError as e:
            self.log_test_result("增强转换器初始化", False, f"导入失败: {str(e)}")
        except Exception as e:
            self.log_test_result("增强转换器初始化", False, f"初始化失败: {str(e)}")
    
    def test_open_pdf2md_engine(self):
        """测试Open-PDF2MD引擎"""
        logger.info("测试Open-PDF2MD引擎...")
        
        try:
            from open_pdf2md_engine import OpenPDF2MDEngine
            engine = OpenPDF2MDEngine()
            
            # 测试组件初始化
            if hasattr(engine, 'table_detector') and engine.table_detector:
                self.log_test_result("表格检测器", True, "表格检测器初始化成功")
            else:
                self.log_test_result("表格检测器", False, "表格检测器初始化失败")
            
            if hasattr(engine, 'formula_detector') and engine.formula_detector:
                self.log_test_result("公式检测器", True, "公式检测器初始化成功")
            else:
                self.log_test_result("公式检测器", False, "公式检测器初始化失败")
            
            # 测试OCR引擎
            if hasattr(engine, 'ocr_engine') and engine.ocr_engine:
                ocr_type = getattr(engine, 'ocr_type', 'unknown')
                self.log_test_result("OCR引擎", True, f"OCR引擎初始化成功 ({ocr_type})")
            else:
                self.log_test_result("OCR引擎", False, "OCR引擎初始化失败")
                
        except ImportError as e:
            self.log_test_result("Open-PDF2MD引擎测试", False, f"导入失败: {str(e)}")
        except Exception as e:
            self.log_test_result("Open-PDF2MD引擎测试", False, f"测试失败: {str(e)}")
    
    def test_pdf_processing_capabilities(self):
        """测试PDF处理能力"""
        logger.info("测试PDF处理能力...")
        
        try:
            # 测试pdfplumber
            import pdfplumber
            self.log_test_result("pdfplumber功能", True, "pdfplumber可用")
            
            # 测试PyMuPDF
            import fitz
            self.log_test_result("PyMuPDF功能", True, "PyMuPDF可用")
            
            # 测试表格检测能力
            try:
                # 创建一个简单的测试，检查pdfplumber的表格检测功能
                # 这里只是检查API是否可用
                self.log_test_result("表格检测能力", True, "表格检测API可用")
            except Exception as e:
                self.log_test_result("表格检测能力", False, f"表格检测测试失败: {str(e)}")
                
        except ImportError as e:
            self.log_test_result("PDF处理能力测试", False, f"依赖导入失败: {str(e)}")
    
    def test_ocr_capabilities(self):
        """测试OCR能力"""
        logger.info("测试OCR能力...")
        
        # 测试Tesseract
        try:
            import pytesseract
            # 简单测试pytesseract是否可用
            self.log_test_result("Tesseract OCR", True, "Tesseract OCR可用")
        except ImportError:
            self.log_test_result("Tesseract OCR", False, "Tesseract OCR不可用")
        
        # 测试PaddleOCR
        try:
            import paddleocr
            self.log_test_result("PaddleOCR", True, "PaddleOCR可用")
        except ImportError:
            self.log_test_result("PaddleOCR", False, "PaddleOCR不可用(可选)")
    
    def test_integration_with_main_app(self):
        """测试与主应用的集成"""
        logger.info("测试与主应用的集成...")
        
        try:
            # 测试markitdown_converter是否能正确导入增强功能
            import markitdown_converter
            self.log_test_result("主应用集成", True, "主应用可以导入")
            
            # 测试是否能创建增强转换器实例
            try:
                from enhanced_markitdown import EnhancedMarkitdownConverter
                converter = EnhancedMarkitdownConverter()
                self.log_test_result("增强转换器集成", True, "增强转换器集成成功")
            except Exception as e:
                self.log_test_result("增强转换器集成", False, f"集成失败: {str(e)}")
                
        except ImportError as e:
            self.log_test_result("主应用集成", False, f"主应用导入失败: {str(e)}")
    
    def test_system_dependencies(self):
        """测试系统依赖"""
        logger.info("测试系统依赖...")
        
        import subprocess
        
        # 测试tesseract命令
        try:
            result = subprocess.run(['tesseract', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.split('\n')[0]
                self.log_test_result("Tesseract命令", True, f"Tesseract可用: {version}")
            else:
                self.log_test_result("Tesseract命令", False, "Tesseract命令不可用")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.log_test_result("Tesseract命令", False, "Tesseract命令不可用")
        
        # 测试poppler工具
        try:
            result = subprocess.run(['pdftoppm', '-h'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 or 'pdftoppm' in result.stderr:
                self.log_test_result("Poppler工具", True, "Poppler工具可用")
            else:
                self.log_test_result("Poppler工具", False, "Poppler工具不可用")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.log_test_result("Poppler工具", False, "Poppler工具不可用")
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行增强功能测试...")
        
        # 运行各项测试
        self.test_dependency_imports()
        self.test_enhanced_converter_init()
        self.test_open_pdf2md_engine()
        self.test_pdf_processing_capabilities()
        self.test_ocr_capabilities()
        self.test_integration_with_main_app()
        self.test_system_dependencies()
        
        # 生成测试报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("生成测试报告...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("增强功能测试报告")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        print("=" * 60)
        
        # 详细结果
        print("\n详细测试结果:")
        print("-" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"{status} {test_name}: {result['message']}")
        
        print("-" * 60)
        
        # 总结
        if failed_tests == 0:
            print("🎉 所有测试通过！增强功能已成功集成。")
        elif passed_tests >= total_tests * 0.8:
            print("✅ 大部分测试通过，增强功能基本可用。")
            print("⚠️ 部分可选功能可能不可用，但不影响核心功能。")
        else:
            print("❌ 多项测试失败，请检查依赖安装和配置。")
            print("💡 建议运行: python install_enhanced_dependencies.py")
        
        print("=" * 60)
        
        return passed_tests, failed_tests


def main():
    """主函数"""
    print("Markitdown Converter - 增强功能测试")
    print("=" * 60)
    
    tester = EnhancedFeaturesTest()
    
    try:
        tester.run_all_tests()
        return 0
    except KeyboardInterrupt:
        print("\n用户取消测试。")
        return 1
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
