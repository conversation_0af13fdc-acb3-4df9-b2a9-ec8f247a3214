// qdbusargument.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusArgument
{
%TypeHeaderCode
#include <qdbusargument.h>
%End

%TypeCode
#include <QStringList>


static PyObject *qdbusargument_add(QDBusArgument *arg, PyObject *obj, int mtype)
{
    int iserr = 0;

    if (PyLong_CheckExact(obj)
#if PY_MAJOR_VERSION < 3
            || PyInt_CheckExact(obj)
#endif
            )
    {
        if (mtype == QMetaType::UChar || mtype == QMetaType::UShort || mtype == QMetaType::UInt || mtype == QMetaType::ULongLong)
        {
            // Handle the unsigned values.
#if defined(HAVE_LONG_LONG)
            unsigned PY_LONG_LONG v = PyLong_AsUnsignedLongLongMask(obj);
#else
            unsigned long v = PyLong_AsUnsignedLongMask(obj);
#endif

            switch (mtype)
            {
            case QMetaType::UChar:
                *arg << (uchar)v;
                break;

            case QMetaType::UShort:
                *arg << (ushort)v;
                break;

            case QMetaType::UInt:
                *arg << (uint)v;
                break;

            case QMetaType::ULongLong:
                *arg << (qulonglong)v;
                break;
            }
        }
        else if (mtype == QMetaType::Short || mtype == QMetaType::Int || mtype == QMetaType::LongLong)
        {
            // Handle the signed values.
#if defined(HAVE_LONG_LONG)
            PY_LONG_LONG v = PyLong_AsLongLong(obj);
#else
            long v = PyLong_AsLong(obj);
#endif

            switch (mtype)
            {
            case QMetaType::Short:
                *arg << (short)v;
                break;

            case QMetaType::Int:
                *arg << (int)v;
                break;

            case QMetaType::LongLong:
                *arg << (qlonglong)v;
                break;
            }
        }
        else
        {
            PyErr_Format(PyExc_ValueError,
                    "%d is an invalid QMetaType::Type for an integer object",
                    mtype);
            iserr = 1;
        }
    }
    else if (mtype == QMetaType::QStringList)
    {
        // A QStringList has to be handled explicitly to prevent it being seen
        // as a vialiant list.

        int value_state;

        QStringList *qsl = reinterpret_cast<QStringList *>(
                sipForceConvertToType(obj, sipType_QStringList, 0,
                        SIP_NOT_NONE, &value_state, &iserr));

        if (!iserr)
        {
            arg->beginArray(QMetaType::QString);

            for (int i = 0; i < qsl->count(); ++i)
                *arg << qsl->at(i);

            arg->endArray();

            sipReleaseType(qsl, sipType_QStringList, value_state);
        }
    }
    else
    {
        int value_state;

        QVariant *qv = reinterpret_cast<QVariant *>(
                sipForceConvertToType(obj, sipType_QVariant, 0, SIP_NOT_NONE,
                        &value_state, &iserr));

        if (!iserr)
        {
            // This is an internal method. If it proves to be a problem then we
            // will have to handle each type explicitly.
            arg->appendVariant(*qv);
            sipReleaseType(qv, sipType_QVariant, value_state);
        }
    }

    if (iserr)
        return 0;

    Py_INCREF(Py_None);
    return Py_None;
}
%End

public:
    QDBusArgument();
    QDBusArgument(const QDBusArgument &other);
    QDBusArgument(SIP_PYOBJECT arg, int id = QMetaType::Int);
%MethodCode
        sipCpp = new QDBusArgument();
        PyObject *res = qdbusargument_add(sipCpp, a0, a1);
        
        if (res)
        {
            Py_DECREF(res);
        }
        else
        {
            delete sipCpp;
            sipCpp = 0;
        }
%End

    ~QDBusArgument();
    SIP_PYOBJECT add(SIP_PYOBJECT arg, int id = QMetaType::Int) /TypeHint=""/;
%MethodCode
        sipRes = qdbusargument_add(sipCpp, a0, a1);
%End

    void beginStructure();
    void endStructure();
    void beginArray(int id);
    void endArray();
    void beginMap(int kid, int vid);
    void endMap();
    void beginMapEntry();
    void endMapEntry();
%If (Qt_5_6_0 -)
    void swap(QDBusArgument &other /Constrained/);
%End
};
