// qlocalserver.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLocalServer : public QObject
{
%TypeHeaderCode
#include <qlocalserver.h>
%End

public:
    explicit QLocalServer(QObject *parent /TransferThis/ = 0);
    virtual ~QLocalServer();
    void close();
    QString errorString() const;
    virtual bool hasPendingConnections() const;
    bool isListening() const;
    bool listen(const QString &name);
    bool listen(qintptr socketDescriptor);
    int maxPendingConnections() const;
    virtual QLocalSocket *nextPendingConnection();
    QString serverName() const;
    QString fullServerName() const;
    QAbstractSocket::SocketError serverError() const;
    void setMaxPendingConnections(int numConnections);
    bool waitForNewConnection(int msecs = 0, bool *timedOut = 0) /ReleaseGIL/;
    static bool removeServer(const QString &name);

signals:
    void newConnection();

protected:
    virtual void incomingConnection(quintptr socketDescriptor);

public:
    enum SocketOption
    {
        UserAccessOption,
        GroupAccessOption,
        OtherAccessOption,
        WorldAccessOption,
    };

    typedef QFlags<QLocalServer::SocketOption> SocketOptions;
    void setSocketOptions(QLocalServer::SocketOptions options);
    QLocalServer::SocketOptions socketOptions() const;
%If (Qt_5_10_0 -)
    qintptr socketDescriptor() const;
%End
};

QFlags<QLocalServer::SocketOption> operator|(QLocalServer::SocketOption f1, QFlags<QLocalServer::SocketOption> f2);
