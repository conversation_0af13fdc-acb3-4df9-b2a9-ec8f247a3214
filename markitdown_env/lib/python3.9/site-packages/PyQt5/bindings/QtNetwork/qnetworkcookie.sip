// qnetworkcookie.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkCookie
{
%TypeHeaderCode
#include <qnetworkcookie.h>
%End

public:
    enum RawForm
    {
        NameAndValueOnly,
        Full,
    };

    QNetworkCookie(const QByteArray &name = QByteArray(), const QByteArray &value = QByteArray());
    QNetworkCookie(const QNetworkCookie &other);
    ~QNetworkCookie();
    bool isSecure() const;
    void setSecure(bool enable);
    bool isSessionCookie() const;
    QDateTime expirationDate() const;
    void setExpirationDate(const QDateTime &date);
    QString domain() const;
    void setDomain(const QString &domain);
    QString path() const;
    void setPath(const QString &path);
    QByteArray name() const;
    void setName(const QByteArray &cookieName);
    QByteArray value() const;
    void setValue(const QByteArray &value);
    QByteArray toRawForm(QNetworkCookie::RawForm form = QNetworkCookie::Full) const;
    static QList<QNetworkCookie> parseCookies(const QByteArray &cookieString);
    bool operator==(const QNetworkCookie &other) const;
    bool operator!=(const QNetworkCookie &other) const;
    bool isHttpOnly() const;
    void setHttpOnly(bool enable);
    void swap(QNetworkCookie &other /Constrained/);
    bool hasSameIdentifier(const QNetworkCookie &other) const;
    void normalize(const QUrl &url);
};
