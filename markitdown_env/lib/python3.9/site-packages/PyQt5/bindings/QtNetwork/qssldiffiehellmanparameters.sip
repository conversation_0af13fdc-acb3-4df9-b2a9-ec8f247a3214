// qssldiffiehellmanparameters.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_8_0 -)
%If (PyQt_SSL)

class QSslDiffieHellmanParameters
{
%TypeHeaderCode
#include <qssldiffiehellmanparameters.h>
%End

public:
    enum Error
    {
        NoError,
        InvalidInputDataError,
        UnsafeParametersError,
    };

    QSslDiffieHellmanParameters();
    QSslDiffieHellmanParameters(const QSslDiffieHellmanParameters &other);
    ~QSslDiffieHellmanParameters();
    void swap(QSslDiffieHellmanParameters &other /Constrained/);
    static QSslDiffieHellmanParameters defaultParameters();
    static QSslDiffieHellmanParameters fromEncoded(const QByteArray &encoded, QSsl::EncodingFormat encoding = QSsl::EncodingFormat::Pem);
    static QSslDiffieHellmanParameters fromEncoded(QIODevice *device, QSsl::EncodingFormat encoding = QSsl::EncodingFormat::Pem) /ReleaseGIL/;
    bool isEmpty() const;
    bool isValid() const;
    QSslDiffieHellmanParameters::Error error() const;
    QString errorString() const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%End
%If (Qt_5_8_0 -)
%If (PyQt_SSL)
bool operator==(const QSslDiffieHellmanParameters &lhs, const QSslDiffieHellmanParameters &rhs);
%End
%End
%If (Qt_5_8_0 -)
%If (PyQt_SSL)
bool operator!=(const QSslDiffieHellmanParameters &lhs, const QSslDiffieHellmanParameters &rhs);
%End
%End
