// qnetworksession.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkSession : public QObject
{
%TypeHeaderCode
#include <qnetworksession.h>
%End

public:
    enum State
    {
        Invalid,
        NotAvailable,
        Connecting,
        Connected,
        Closing,
        Disconnected,
        Roaming,
    };

    enum SessionError
    {
        UnknownSessionError,
        SessionAbortedError,
        RoamingError,
        OperationNotSupportedError,
        InvalidConfigurationError,
    };

    QNetworkSession(const QNetworkConfiguration &connConfig, QObject *parent /TransferThis/ = 0);
    virtual ~QNetworkSession();
    bool isOpen() const;
    QNetworkConfiguration configuration() const;
    QNetworkInterface interface() const;
    QNetworkSession::State state() const;
    QNetworkSession::SessionError error() const;
    QString errorString() const;
    QVariant sessionProperty(const QString &key) const;
    void setSessionProperty(const QString &key, const QVariant &value);
    quint64 bytesWritten() const;
    quint64 bytesReceived() const;
    quint64 activeTime() const;
    bool waitForOpened(int msecs = 30000) /ReleaseGIL/;

public slots:
    void open();
    void close();
    void stop();
    void migrate();
    void ignore();
    void accept();
    void reject();

signals:
    void stateChanged(QNetworkSession::State);
    void opened();
    void closed();
    void error(QNetworkSession::SessionError);
    void preferredConfigurationChanged(const QNetworkConfiguration &config, bool isSeamless);
    void newConfigurationActivated();

protected:
    virtual void connectNotify(const QMetaMethod &signal);
    virtual void disconnectNotify(const QMetaMethod &signal);

public:
    enum UsagePolicy
    {
        NoPolicy,
        NoBackgroundTrafficPolicy,
    };

    typedef QFlags<QNetworkSession::UsagePolicy> UsagePolicies;
    QNetworkSession::UsagePolicies usagePolicies() const;

signals:
    void usagePoliciesChanged(QNetworkSession::UsagePolicies usagePolicies);
};
