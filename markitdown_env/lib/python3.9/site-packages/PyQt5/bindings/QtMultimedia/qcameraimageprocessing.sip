// qcameraimageprocessing.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraImageProcessing : public QObject
{
%TypeHeaderCode
#include <qcameraimageprocessing.h>
%End

public:
    enum WhiteBalanceMode
    {
        WhiteBalanceAuto,
        WhiteBalanceManual,
        WhiteBalanceSunlight,
        WhiteBalanceCloudy,
        WhiteBalanceShade,
        WhiteBalanceTungsten,
        WhiteBalanceFluorescent,
        WhiteBalanceFlash,
        WhiteBalanceSunset,
        WhiteBalanceVendor,
    };

    bool isAvailable() const;
    QCameraImageProcessing::WhiteBalanceMode whiteBalanceMode() const;
    void setWhiteBalanceMode(QCameraImageProcessing::WhiteBalanceMode mode);
    bool isWhiteBalanceModeSupported(QCameraImageProcessing::WhiteBalanceMode mode) const;
    qreal manualWhiteBalance() const;
    void setManualWhiteBalance(qreal colorTemperature);
    qreal contrast() const;
    void setContrast(qreal value);
    qreal saturation() const;
    void setSaturation(qreal value);
    qreal sharpeningLevel() const;
    void setSharpeningLevel(qreal value);
    qreal denoisingLevel() const;
    void setDenoisingLevel(qreal value);

private:
    QCameraImageProcessing(QCamera *camera);

protected:
%If (Qt_5_14_0 -)
    virtual ~QCameraImageProcessing();
%End

private:
%If (- Qt_5_14_0)
    virtual ~QCameraImageProcessing();
%End
    QCameraImageProcessing(const QCameraImageProcessing &);

public:
%If (Qt_5_5_0 -)

    enum ColorFilter
    {
        ColorFilterNone,
        ColorFilterGrayscale,
        ColorFilterNegative,
        ColorFilterSolarize,
        ColorFilterSepia,
        ColorFilterPosterize,
        ColorFilterWhiteboard,
        ColorFilterBlackboard,
        ColorFilterAqua,
        ColorFilterVendor,
    };

%End
%If (Qt_5_5_0 -)
    QCameraImageProcessing::ColorFilter colorFilter() const;
%End
%If (Qt_5_5_0 -)
    void setColorFilter(QCameraImageProcessing::ColorFilter filter);
%End
%If (Qt_5_5_0 -)
    bool isColorFilterSupported(QCameraImageProcessing::ColorFilter filter) const;
%End
%If (Qt_5_7_0 -)
    qreal brightness() const;
%End
%If (Qt_5_7_0 -)
    void setBrightness(qreal value);
%End
};
