// qcameraviewfindersettings.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QCameraViewfinderSettings
{
%TypeHeaderCode
#include <qcameraviewfindersettings.h>
%End

public:
    QCameraViewfinderSettings();
    QCameraViewfinderSettings(const QCameraViewfinderSettings &other);
    ~QCameraViewfinderSettings();
    void swap(QCameraViewfinderSettings &other /Constrained/);
    bool isNull() const;
    QSize resolution() const;
    void setResolution(const QSize &);
    void setResolution(int width, int height);
    qreal minimumFrameRate() const;
    void setMinimumFrameRate(qreal rate);
    qreal maximumFrameRate() const;
    void setMaximumFrameRate(qreal rate);
    QVideoFrame::PixelFormat pixelFormat() const;
    void setPixelFormat(QVideoFrame::PixelFormat format);
    QSize pixelAspectRatio() const;
    void setPixelAspectRatio(const QSize &ratio);
    void setPixelAspectRatio(int horizontal, int vertical);
};

%End
%If (Qt_5_5_0 -)
bool operator==(const QCameraViewfinderSettings &lhs, const QCameraViewfinderSettings &rhs);
%End
%If (Qt_5_5_0 -)
bool operator!=(const QCameraViewfinderSettings &lhs, const QCameraViewfinderSettings &rhs);
%End
