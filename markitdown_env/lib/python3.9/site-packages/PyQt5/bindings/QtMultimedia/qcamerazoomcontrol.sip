// qcamerazoomcontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraZoomControl : public QMediaControl
{
%TypeHeaderCode
#include <qcamerazoomcontrol.h>
%End

public:
    virtual ~QCameraZoomControl();
    virtual qreal maximumOpticalZoom() const = 0;
    virtual qreal maximumDigitalZoom() const = 0;
    virtual qreal requestedOpticalZoom() const = 0;
    virtual qreal requestedDigitalZoom() const = 0;
    virtual qreal currentOpticalZoom() const = 0;
    virtual qreal currentDigitalZoom() const = 0;
    virtual void zoomTo(qreal optical, qreal digital) = 0;

signals:
    void maximumOpticalZoomChanged(qreal);
    void maximumDigitalZoomChanged(qreal);
    void requestedOpticalZoomChanged(qreal opticalZoom);
    void requestedDigitalZoomChanged(qreal digitalZoom);
    void currentOpticalZoomChanged(qreal opticalZoom);
    void currentDigitalZoomChanged(qreal digitalZoom);

protected:
    explicit QCameraZoomControl(QObject *parent /TransferThis/ = 0);
};
