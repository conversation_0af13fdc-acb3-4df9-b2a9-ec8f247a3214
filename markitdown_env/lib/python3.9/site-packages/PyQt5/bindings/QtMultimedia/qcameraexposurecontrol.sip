// qcameraexposurecontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraExposureControl : public QMediaControl
{
%TypeHeaderCode
#include <qcameraexposurecontrol.h>
%End

public:
    virtual ~QCameraExposureControl();

    enum ExposureParameter
    {
        ISO,
        Aperture,
        ShutterSpeed,
        ExposureCompensation,
        FlashPower,
        FlashCompensation,
        TorchPower,
        SpotMeteringPoint,
        ExposureMode,
        MeteringMode,
        ExtendedExposureParameter,
    };

    virtual bool isParameterSupported(QCameraExposureControl::ExposureParameter parameter) const = 0;
    virtual QVariantList supportedParameterRange(QCameraExposureControl::ExposureParameter parameter, bool *continuous) const = 0;
    virtual QVariant requestedValue(QCameraExposureControl::ExposureParameter parameter) const = 0;
    virtual QVariant actualValue(QCameraExposureControl::ExposureParameter parameter) const = 0;
    virtual bool setValue(QCameraExposureControl::ExposureParameter parameter, const QVariant &value) = 0;

signals:
    void requestedValueChanged(int parameter);
    void actualValueChanged(int parameter);
    void parameterRangeChanged(int parameter);

protected:
    explicit QCameraExposureControl(QObject *parent /TransferThis/ = 0);
};
