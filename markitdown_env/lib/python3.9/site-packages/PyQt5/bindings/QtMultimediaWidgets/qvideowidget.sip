// qvideowidget.sip generated by MetaSIP
//
// This file is part of the QtMultimediaWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVideoWidget : public QWidget, public QMediaBindableInterface
{
%TypeHeaderCode
#include <qvideowidget.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QVideoWidget, &sipType_QVideoWidget, 3, 1},
        {sipName_QGraphicsVideoItem, &sipType_QGraphicsVideoItem, -1, 2},
        {sipName_QVideoWidgetControl, &sipType_QVideoWidgetControl, -1, -1},
        {sipName_QCameraViewfinder, &sipType_QCameraViewfinder, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
%If (Qt_5_6_1 -)
    explicit QVideoWidget(QWidget *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QVideoWidget(QWidget *parent /TransferThis/ = 0);
%End
    virtual ~QVideoWidget();
    virtual QMediaObject *mediaObject() const;
    Qt::AspectRatioMode aspectRatioMode() const;
    int brightness() const;
    int contrast() const;
    int hue() const;
    int saturation() const;
    virtual QSize sizeHint() const;

public slots:
    void setFullScreen(bool fullScreen);
    void setAspectRatioMode(Qt::AspectRatioMode mode);
    void setBrightness(int brightness);
    void setContrast(int contrast);
    void setHue(int hue);
    void setSaturation(int saturation);

signals:
    void fullScreenChanged(bool fullScreen);
    void brightnessChanged(int brightness);
    void contrastChanged(int contrast);
    void hueChanged(int hue);
    void saturationChanged(int saturation);

protected:
    virtual bool event(QEvent *event);
    virtual void showEvent(QShowEvent *event);
    virtual void hideEvent(QHideEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void moveEvent(QMoveEvent *event);
    virtual void paintEvent(QPaintEvent *event);
    virtual bool setMediaObject(QMediaObject *object);

public:
%If (Qt_5_15_0 -)
    QAbstractVideoSurface *videoSurface() const;
%End
};
