// qiodevice.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QIODevice : public QObject
{
%TypeHeaderCode
#include <qiodevice.h>
%End

public:
    enum OpenModeFlag
    {
        NotO<PERSON>,
        ReadOnly,
        <PERSON><PERSON><PERSON><PERSON>ly,
        <PERSON>Write,
        Append,
        Truncate,
        Text,
        Unbuffered,
%If (Qt_5_11_0 -)
        NewOnly,
%End
%If (Qt_5_11_0 -)
        ExistingOnly,
%End
    };

    typedef QFlags<QIODevice::OpenModeFlag> OpenMode;
    QIODevice();
    explicit QIODevice(QObject *parent /TransferThis/);
    virtual ~QIODevice();
    QIODevice::OpenMode openMode() const;
    void setTextModeEnabled(bool enabled);
    bool isTextModeEnabled() const;
    bool isOpen() const;
    bool isReadable() const;
    bool isWritable() const;
    virtual bool isSequential() const;
    virtual bool open(QIODevice::OpenMode mode) /ReleaseGIL/;
    virtual void close() /ReleaseGIL/;
    virtual qint64 pos() const;
    virtual qint64 size() const;
    virtual bool seek(qint64 pos) /ReleaseGIL/;
    virtual bool atEnd() const;
    virtual bool reset();
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    SIP_PYOBJECT read(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/;
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->read(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    QByteArray readAll() /ReleaseGIL/;
    SIP_PYOBJECT readLine(qint64 maxlen=0) /TypeHint="Py_v3:bytes;str",ReleaseGIL/;
%MethodCode
        // The two C++ overloads would have the same Python signature so we get most of
        // the combined functionality by treating an argument of 0 (the default) as
        // meaning return a QByteArray of any length.  Otherwise it is treated as a
        // maximum buffer size and a Python string is returned.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else if (a0 == 0)
        {
            QByteArray *ba;
        
            Py_BEGIN_ALLOW_THREADS
            ba = new QByteArray(sipCpp->readLine(a0));
            Py_END_ALLOW_THREADS
        
            sipRes = sipBuildResult(&sipIsErr, "N", ba, sipType_QByteArray, 0);
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->readLine(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual bool canReadLine() const;
    QByteArray peek(qint64 maxlen) /ReleaseGIL/;
    qint64 write(const QByteArray &data) /ReleaseGIL/;
    virtual bool waitForReadyRead(int msecs) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs) /ReleaseGIL/;
    void ungetChar(char c);
    bool putChar(char c);
    bool getChar(char *c /Encoding="None",Out/);
    QString errorString() const;

signals:
    void readyRead();
    void bytesWritten(qint64 bytes);
    void aboutToClose();
    void readChannelFinished();

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) = 0 /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtect_readData(s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

%VirtualCatcherCode
        PyObject *result = sipCallMethod(&sipIsErr, sipMethod, "n", a1);
        
        if (result != NULL)
        {
            PyObject *buf;
        
            sipParseResult(&sipIsErr, sipMethod, result, "O", &buf);
        
            if (buf == Py_None)
                sipRes = -1L;
            else if (!SIPBytes_Check(buf))
            {
                sipBadCatcherResult(sipMethod);
                sipIsErr = 1;
            }
            else
            {
                memcpy(a0, SIPBytes_AsString(buf), SIPBytes_Size(buf));
                sipRes = SIPBytes_Size(buf);
            }
        
            Py_DECREF(buf);
            Py_DECREF(result);
        }
%End

    virtual SIP_PYOBJECT readLineData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QIODevice::readLineData(s, a0) : sipCpp->readLineData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readLineData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

%VirtualCatcherCode
        PyObject *result = sipCallMethod(&sipIsErr, sipMethod, "n", a1);
        
        if (result != NULL)
        {
            PyObject *buf;
        
            sipParseResult(&sipIsErr, sipMethod, result, "O", &buf);
        
            if (buf == Py_None)
                sipRes = -1L;
            else if (!SIPBytes_Check(buf))
            {
                sipBadCatcherResult(sipMethod);
                sipIsErr = 1;
            }
            else
            {
                memcpy(a0, SIPBytes_AsString(buf), SIPBytes_Size(buf));
                sipRes = SIPBytes_Size(buf);
            }
        
            Py_DECREF(buf);
            Py_DECREF(result);
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) = 0;
    void setOpenMode(QIODevice::OpenMode openMode);
    void setErrorString(const QString &errorString);

public:
%If (Qt_5_7_0 -)
    int readChannelCount() const;
%End
%If (Qt_5_7_0 -)
    int writeChannelCount() const;
%End
%If (Qt_5_7_0 -)
    int currentReadChannel() const;
%End
%If (Qt_5_7_0 -)
    void setCurrentReadChannel(int channel);
%End
%If (Qt_5_7_0 -)
    int currentWriteChannel() const;
%End
%If (Qt_5_7_0 -)
    void setCurrentWriteChannel(int channel);
%End
%If (Qt_5_7_0 -)
    void startTransaction();
%End
%If (Qt_5_7_0 -)
    void commitTransaction();
%End
%If (Qt_5_7_0 -)
    void rollbackTransaction();
%End
%If (Qt_5_7_0 -)
    bool isTransactionStarted() const;
%End

signals:
%If (Qt_5_7_0 -)
    void channelReadyRead(int channel);
%End
%If (Qt_5_7_0 -)
    void channelBytesWritten(int channel, qint64 bytes);
%End

public:
%If (Qt_5_10_0 -)
    qint64 skip(qint64 maxSize);
%End
};

QFlags<QIODevice::OpenModeFlag> operator|(QIODevice::OpenModeFlag f1, QFlags<QIODevice::OpenModeFlag> f2);
