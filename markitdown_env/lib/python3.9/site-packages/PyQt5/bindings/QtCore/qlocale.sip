// qlocale.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLocale
{
%TypeHeaderCode
#include <qlocale.h>
%End

public:
    enum Language
    {
        C,
        Abkhazian,
        Afan,
        Afar,
        Afrikaans,
        Albanian,
        Amharic,
        Arabic,
        Armenian,
        Assamese,
        Aymara,
        Azerbaijani,
        Bashkir,
        Basque,
        Bengali,
        Bhutani,
        Bihari,
        Bislama,
        Breton,
        Bulgarian,
        Burmese,
        Byelorussian,
        Cambodian,
        Catalan,
        Chinese,
        Corsican,
        Croatian,
        Czech,
        Danish,
        Dutch,
        English,
        Esperanto,
        Estonian,
        Faroese,
        Finnish,
        French,
        Frisian,
        Gaelic,
        Galician,
        Georgian,
        German,
        Greek,
        Greenlandic,
        Guarani,
        Gujarati,
        Hausa,
        Hebrew,
        Hindi,
        Hungarian,
        Icelandic,
        Indonesian,
        Interlingua,
        Interlingue,
        Inuktitut,
        Inupiak,
        Irish,
        Italian,
        Japanese,
        Javanese,
        Kannada,
        Kashmiri,
        Kazakh,
        Kinyarwanda,
        Kirghiz,
        Korean,
        Kurdish,
        Kurundi,
        Latin,
        Latvian,
        Lingala,
        Lithuanian,
        Macedonian,
        Malagasy,
        Malay,
        Malayalam,
        Maltese,
        Maori,
        Marathi,
        Moldavian,
        Mongolian,
        NauruLanguage,
        Nepali,
        Norwegian,
        Occitan,
        Oriya,
        Pashto,
        Persian,
        Polish,
        Portuguese,
        Punjabi,
        Quechua,
        RhaetoRomance,
        Romanian,
        Russian,
        Samoan,
        Sanskrit,
        Serbian,
        SerboCroatian,
        Shona,
        Sindhi,
        Slovak,
        Slovenian,
        Somali,
        Spanish,
        Sundanese,
        Swahili,
        Swedish,
        Tagalog,
        Tajik,
        Tamil,
        Tatar,
        Telugu,
        Thai,
        Tibetan,
        Tigrinya,
        Tsonga,
        Turkish,
        Turkmen,
        Twi,
        Uigur,
        Ukrainian,
        Urdu,
        Uzbek,
        Vietnamese,
        Volapuk,
        Welsh,
        Wolof,
        Xhosa,
        Yiddish,
        Yoruba,
        Zhuang,
        Zulu,
        Bosnian,
        Divehi,
        Manx,
        Cornish,
        LastLanguage,
        NorwegianBokmal,
        NorwegianNynorsk,
        Akan,
        Konkani,
        Ga,
        Igbo,
        Kamba,
        Syriac,
        Blin,
        Geez,
        Koro,
        Sidamo,
        Atsam,
        Tigre,
        Jju,
        Friulian,
        Venda,
        Ewe,
        Walamo,
        Hawaiian,
        Tyap,
        Chewa,
        Filipino,
        SwissGerman,
        SichuanYi,
        Kpelle,
        LowGerman,
        SouthNdebele,
        NorthernSotho,
        NorthernSami,
        Taroko,
        Gusii,
        Taita,
        Fulah,
        Kikuyu,
        Samburu,
        Sena,
        NorthNdebele,
        Rombo,
        Tachelhit,
        Kabyle,
        Nyankole,
        Bena,
        Vunjo,
        Bambara,
        Embu,
        Cherokee,
        Morisyen,
        Makonde,
        Langi,
        Ganda,
        Bemba,
        Kabuverdianu,
        Meru,
        Kalenjin,
        Nama,
        Machame,
        Colognian,
        Masai,
        Soga,
        Luyia,
        Asu,
        Teso,
        Saho,
        KoyraChiini,
        Rwa,
        Luo,
        Chiga,
        CentralMoroccoTamazight,
        KoyraboroSenni,
        Shambala,
        AnyLanguage,
        Rundi,
        Bodo,
        Aghem,
        Basaa,
        Zarma,
        Duala,
        JolaFonyi,
        Ewondo,
        Bafia,
        LubaKatanga,
        MakhuwaMeetto,
        Mundang,
        Kwasio,
        Nuer,
        Sakha,
        Sangu,
        CongoSwahili,
        Tasawaq,
        Vai,
        Walser,
        Yangben,
        Oromo,
        Dzongkha,
        Belarusian,
        Khmer,
        Fijian,
        WesternFrisian,
        Lao,
        Marshallese,
        Romansh,
        Sango,
        Ossetic,
        SouthernSotho,
        Tswana,
        Sinhala,
        Swati,
        Sardinian,
        Tongan,
        Tahitian,
        Nyanja,
        Avaric,
        Chamorro,
        Chechen,
        Church,
        Chuvash,
        Cree,
        Haitian,
        Herero,
        HiriMotu,
        Kanuri,
        Komi,
        Kongo,
        Kwanyama,
        Limburgish,
        Luxembourgish,
        Navaho,
        Ndonga,
        Ojibwa,
        Pali,
        Walloon,
        Avestan,
        Asturian,
        Ngomba,
        Kako,
        Meta,
        Ngiemboon,
%If (Qt_5_1_0 -)
        Uighur,
%End
%If (Qt_5_1_0 -)
        Aragonese,
%End
%If (Qt_5_1_0 -)
        Akkadian,
%End
%If (Qt_5_1_0 -)
        AncientEgyptian,
%End
%If (Qt_5_1_0 -)
        AncientGreek,
%End
%If (Qt_5_1_0 -)
        Aramaic,
%End
%If (Qt_5_1_0 -)
        Balinese,
%End
%If (Qt_5_1_0 -)
        Bamun,
%End
%If (Qt_5_1_0 -)
        BatakToba,
%End
%If (Qt_5_1_0 -)
        Buginese,
%End
%If (Qt_5_1_0 -)
        Buhid,
%End
%If (Qt_5_1_0 -)
        Carian,
%End
%If (Qt_5_1_0 -)
        Chakma,
%End
%If (Qt_5_1_0 -)
        ClassicalMandaic,
%End
%If (Qt_5_1_0 -)
        Coptic,
%End
%If (Qt_5_1_0 -)
        Dogri,
%End
%If (Qt_5_1_0 -)
        EasternCham,
%End
%If (Qt_5_1_0 -)
        EasternKayah,
%End
%If (Qt_5_1_0 -)
        Etruscan,
%End
%If (Qt_5_1_0 -)
        Gothic,
%End
%If (Qt_5_1_0 -)
        Hanunoo,
%End
%If (Qt_5_1_0 -)
        Ingush,
%End
%If (Qt_5_1_0 -)
        LargeFloweryMiao,
%End
%If (Qt_5_1_0 -)
        Lepcha,
%End
%If (Qt_5_1_0 -)
        Limbu,
%End
%If (Qt_5_1_0 -)
        Lisu,
%End
%If (Qt_5_1_0 -)
        Lu,
%End
%If (Qt_5_1_0 -)
        Lycian,
%End
%If (Qt_5_1_0 -)
        Lydian,
%End
%If (Qt_5_1_0 -)
        Mandingo,
%End
%If (Qt_5_1_0 -)
        Manipuri,
%End
%If (Qt_5_1_0 -)
        Meroitic,
%End
%If (Qt_5_1_0 -)
        NorthernThai,
%End
%If (Qt_5_1_0 -)
        OldIrish,
%End
%If (Qt_5_1_0 -)
        OldNorse,
%End
%If (Qt_5_1_0 -)
        OldPersian,
%End
%If (Qt_5_1_0 -)
        OldTurkish,
%End
%If (Qt_5_1_0 -)
        Pahlavi,
%End
%If (Qt_5_1_0 -)
        Parthian,
%End
%If (Qt_5_1_0 -)
        Phoenician,
%End
%If (Qt_5_1_0 -)
        PrakritLanguage,
%End
%If (Qt_5_1_0 -)
        Rejang,
%End
%If (Qt_5_1_0 -)
        Sabaean,
%End
%If (Qt_5_1_0 -)
        Samaritan,
%End
%If (Qt_5_1_0 -)
        Santali,
%End
%If (Qt_5_1_0 -)
        Saurashtra,
%End
%If (Qt_5_1_0 -)
        Sora,
%End
%If (Qt_5_1_0 -)
        Sylheti,
%End
%If (Qt_5_1_0 -)
        Tagbanwa,
%End
%If (Qt_5_1_0 -)
        TaiDam,
%End
%If (Qt_5_1_0 -)
        TaiNua,
%End
%If (Qt_5_1_0 -)
        Ugaritic,
%End
%If (Qt_5_3_0 -)
        Akoose,
%End
%If (Qt_5_3_0 -)
        Lakota,
%End
%If (Qt_5_3_0 -)
        StandardMoroccanTamazight,
%End
%If (Qt_5_5_0 -)
        Mapuche,
%End
%If (Qt_5_5_0 -)
        CentralKurdish,
%End
%If (Qt_5_5_0 -)
        LowerSorbian,
%End
%If (Qt_5_5_0 -)
        UpperSorbian,
%End
%If (Qt_5_5_0 -)
        Kenyang,
%End
%If (Qt_5_5_0 -)
        Mohawk,
%End
%If (Qt_5_5_0 -)
        Nko,
%End
%If (Qt_5_5_0 -)
        Prussian,
%End
%If (Qt_5_5_0 -)
        Kiche,
%End
%If (Qt_5_5_0 -)
        SouthernSami,
%End
%If (Qt_5_5_0 -)
        LuleSami,
%End
%If (Qt_5_5_0 -)
        InariSami,
%End
%If (Qt_5_5_0 -)
        SkoltSami,
%End
%If (Qt_5_5_0 -)
        Warlpiri,
%End
%If (Qt_5_5_0 -)
        ManichaeanMiddlePersian,
%End
%If (Qt_5_5_0 -)
        Mende,
%End
%If (Qt_5_5_0 -)
        AncientNorthArabian,
%End
%If (Qt_5_5_0 -)
        LinearA,
%End
%If (Qt_5_5_0 -)
        HmongNjua,
%End
%If (Qt_5_5_0 -)
        Ho,
%End
%If (Qt_5_5_0 -)
        Lezghian,
%End
%If (Qt_5_5_0 -)
        Bassa,
%End
%If (Qt_5_5_0 -)
        Mono,
%End
%If (Qt_5_5_0 -)
        TedimChin,
%End
%If (Qt_5_5_0 -)
        Maithili,
%End
%If (Qt_5_7_0 -)
        Ahom,
%End
%If (Qt_5_7_0 -)
        AmericanSignLanguage,
%End
%If (Qt_5_7_0 -)
        ArdhamagadhiPrakrit,
%End
%If (Qt_5_7_0 -)
        Bhojpuri,
%End
%If (Qt_5_7_0 -)
        HieroglyphicLuwian,
%End
%If (Qt_5_7_0 -)
        LiteraryChinese,
%End
%If (Qt_5_7_0 -)
        Mazanderani,
%End
%If (Qt_5_7_0 -)
        Mru,
%End
%If (Qt_5_7_0 -)
        Newari,
%End
%If (Qt_5_7_0 -)
        NorthernLuri,
%End
%If (Qt_5_7_0 -)
        Palauan,
%End
%If (Qt_5_7_0 -)
        Papiamento,
%End
%If (Qt_5_7_0 -)
        Saraiki,
%End
%If (Qt_5_7_0 -)
        TokelauLanguage,
%End
%If (Qt_5_7_0 -)
        TokPisin,
%End
%If (Qt_5_7_0 -)
        TuvaluLanguage,
%End
%If (Qt_5_7_0 -)
        UncodedLanguages,
%End
%If (Qt_5_7_0 -)
        Cantonese,
%End
%If (Qt_5_7_0 -)
        Osage,
%End
%If (Qt_5_7_0 -)
        Tangut,
%End
%If (Qt_5_13_0 -)
        Ido,
%End
%If (Qt_5_13_0 -)
        Lojban,
%End
%If (Qt_5_13_0 -)
        Sicilian,
%End
%If (Qt_5_13_0 -)
        SouthernKurdish,
%End
%If (Qt_5_13_0 -)
        WesternBalochi,
%End
%If (Qt_5_14_0 -)
        Cebuano,
%End
%If (Qt_5_14_0 -)
        Erzya,
%End
%If (Qt_5_14_0 -)
        Chickasaw,
%End
%If (Qt_5_14_0 -)
        Muscogee,
%End
%If (Qt_5_14_0 -)
        Silesian,
%End
%If (Qt_5_15_2 -)
        NigerianPidgin,
%End
    };

    enum Country
    {
        AnyCountry,
        Afghanistan,
        Albania,
        Algeria,
        AmericanSamoa,
        Andorra,
        Angola,
        Anguilla,
        Antarctica,
        AntiguaAndBarbuda,
        Argentina,
        Armenia,
        Aruba,
        Australia,
        Austria,
        Azerbaijan,
        Bahamas,
        Bahrain,
        Bangladesh,
        Barbados,
        Belarus,
        Belgium,
        Belize,
        Benin,
        Bermuda,
        Bhutan,
        Bolivia,
        BosniaAndHerzegowina,
        Botswana,
        BouvetIsland,
        Brazil,
        BritishIndianOceanTerritory,
        Bulgaria,
        BurkinaFaso,
        Burundi,
        Cambodia,
        Cameroon,
        Canada,
        CapeVerde,
        CaymanIslands,
        CentralAfricanRepublic,
        Chad,
        Chile,
        China,
        ChristmasIsland,
        CocosIslands,
        Colombia,
        Comoros,
        DemocraticRepublicOfCongo,
        PeoplesRepublicOfCongo,
        CookIslands,
        CostaRica,
        IvoryCoast,
        Croatia,
        Cuba,
        Cyprus,
        CzechRepublic,
        Denmark,
        Djibouti,
        Dominica,
        DominicanRepublic,
        EastTimor,
        Ecuador,
        Egypt,
        ElSalvador,
        EquatorialGuinea,
        Eritrea,
        Estonia,
        Ethiopia,
        FalklandIslands,
        FaroeIslands,
        Finland,
        France,
        FrenchGuiana,
        FrenchPolynesia,
        FrenchSouthernTerritories,
        Gabon,
        Gambia,
        Georgia,
        Germany,
        Ghana,
        Gibraltar,
        Greece,
        Greenland,
        Grenada,
        Guadeloupe,
        Guam,
        Guatemala,
        Guinea,
        GuineaBissau,
        Guyana,
        Haiti,
        HeardAndMcDonaldIslands,
        Honduras,
        HongKong,
        Hungary,
        Iceland,
        India,
        Indonesia,
        Iran,
        Iraq,
        Ireland,
        Israel,
        Italy,
        Jamaica,
        Japan,
        Jordan,
        Kazakhstan,
        Kenya,
        Kiribati,
        DemocraticRepublicOfKorea,
        RepublicOfKorea,
        Kuwait,
        Kyrgyzstan,
        Latvia,
        Lebanon,
        Lesotho,
        Liberia,
        Liechtenstein,
        Lithuania,
        Luxembourg,
        Macau,
        Macedonia,
        Madagascar,
        Malawi,
        Malaysia,
        Maldives,
        Mali,
        Malta,
        MarshallIslands,
        Martinique,
        Mauritania,
        Mauritius,
        Mayotte,
        Mexico,
        Micronesia,
        Moldova,
        Monaco,
        Mongolia,
        Montserrat,
        Morocco,
        Mozambique,
        Myanmar,
        Namibia,
        NauruCountry,
        Nepal,
        Netherlands,
        NewCaledonia,
        NewZealand,
        Nicaragua,
        Niger,
        Nigeria,
        Niue,
        NorfolkIsland,
        NorthernMarianaIslands,
        Norway,
        Oman,
        Pakistan,
        Palau,
        Panama,
        PapuaNewGuinea,
        Paraguay,
        Peru,
        Philippines,
        Pitcairn,
        Poland,
        Portugal,
        PuertoRico,
        Qatar,
        Reunion,
        Romania,
        RussianFederation,
        Rwanda,
        SaintKittsAndNevis,
        Samoa,
        SanMarino,
        SaoTomeAndPrincipe,
        SaudiArabia,
        Senegal,
        Seychelles,
        SierraLeone,
        Singapore,
        Slovakia,
        Slovenia,
        SolomonIslands,
        Somalia,
        SouthAfrica,
        SouthGeorgiaAndTheSouthSandwichIslands,
        Spain,
        SriLanka,
        Sudan,
        Suriname,
        SvalbardAndJanMayenIslands,
        Swaziland,
        Sweden,
        Switzerland,
        SyrianArabRepublic,
        Taiwan,
        Tajikistan,
        Tanzania,
        Thailand,
        Togo,
        Tokelau,
        TrinidadAndTobago,
        Tunisia,
        Turkey,
        Turkmenistan,
        TurksAndCaicosIslands,
        Tuvalu,
        Uganda,
        Ukraine,
        UnitedArabEmirates,
        UnitedKingdom,
        UnitedStates,
        UnitedStatesMinorOutlyingIslands,
        Uruguay,
        Uzbekistan,
        Vanuatu,
        VaticanCityState,
        Venezuela,
        BritishVirginIslands,
        WallisAndFutunaIslands,
        WesternSahara,
        Yemen,
        Zambia,
        Zimbabwe,
        Montenegro,
        Serbia,
        SaintBarthelemy,
        SaintMartin,
        LatinAmericaAndTheCaribbean,
        LastCountry,
        Brunei,
        CongoKinshasa,
        CongoBrazzaville,
        Fiji,
        Guernsey,
        NorthKorea,
        SouthKorea,
        Laos,
        Libya,
        CuraSao,
        PalestinianTerritories,
        Russia,
        SaintLucia,
        SaintVincentAndTheGrenadines,
        SaintHelena,
        SaintPierreAndMiquelon,
        Syria,
        Tonga,
        Vietnam,
        UnitedStatesVirginIslands,
        CanaryIslands,
        ClippertonIsland,
        AscensionIsland,
        AlandIslands,
        DiegoGarcia,
        CeutaAndMelilla,
        IsleOfMan,
        Jersey,
        TristanDaCunha,
        SouthSudan,
        Bonaire,
        SintMaarten,
%If (Qt_5_2_0 -)
        Kosovo,
%End
%If (Qt_5_7_0 -)
        TokelauCountry,
%End
%If (Qt_5_7_0 -)
        TuvaluCountry,
%End
%If (Qt_5_7_0 -)
        EuropeanUnion,
%End
%If (Qt_5_7_0 -)
        OutlyingOceania,
%End
%If (Qt_5_12_0 -)
        LatinAmerica,
%End
%If (Qt_5_12_0 -)
        World,
%End
%If (Qt_5_12_0 -)
        Europe,
%End
    };

    enum NumberOption
    {
        OmitGroupSeparator,
        RejectGroupSeparator,
%If (Qt_5_7_0 -)
        DefaultNumberOptions,
%End
%If (Qt_5_7_0 -)
        OmitLeadingZeroInExponent,
%End
%If (Qt_5_7_0 -)
        RejectLeadingZeroInExponent,
%End
%If (Qt_5_9_0 -)
        IncludeTrailingZeroesAfterDot,
%End
%If (Qt_5_9_0 -)
        RejectTrailingZeroesAfterDot,
%End
    };

    typedef QFlags<QLocale::NumberOption> NumberOptions;
    QLocale();
    QLocale(const QString &name);
    QLocale(QLocale::Language language, QLocale::Country country = QLocale::AnyCountry);
    QLocale(const QLocale &other);
    ~QLocale();
    QLocale::Language language() const;
    QLocale::Country country() const;
    QString name() const;
    short toShort(const QString &s, bool *ok = 0) const;
    ushort toUShort(const QString &s, bool *ok = 0) const;
    int toInt(const QString &s, bool *ok = 0) const;
    uint toUInt(const QString &s, bool *ok = 0) const;
    qlonglong toLongLong(const QString &s, bool *ok = 0) const;
    qulonglong toULongLong(const QString &s, bool *ok = 0) const;
    float toFloat(const QString &s, bool *ok = 0) const;
    double toDouble(const QString &s, bool *ok = 0) const;
    QString toString(double i /Constrained/, char format = 'g', int precision = 6) const;
    bool operator==(const QLocale &other) const;
    bool operator!=(const QLocale &other) const;
    static QString languageToString(QLocale::Language language);
    static QString countryToString(QLocale::Country country);
    static void setDefault(const QLocale &locale);
    static QLocale c();
    static QLocale system();

    enum FormatType
    {
        LongFormat,
        ShortFormat,
        NarrowFormat,
    };

    QString toString(const QDateTime &dateTime, const QString &format) const;
%If (Qt_5_14_0 -)
    QString toString(const QDateTime &dateTime, const QString &formatStr, QCalendar cal) const;
%MethodCode
        // QStringView has issues being implemented as a mapped type.
        sipRes = new QString(sipCpp->toString(*a0, QStringView(*a1), *a2));
%End

%End
    QString toString(const QDateTime &dateTime, QLocale::FormatType format = QLocale::LongFormat) const;
%If (Qt_5_14_0 -)
    QString toString(const QDateTime &dateTime, QLocale::FormatType format, QCalendar cal) const;
%End
    QString toString(const QDate &date, const QString &formatStr) const;
%If (Qt_5_14_0 -)
    QString toString(const QDate &date, const QString &formatStr, QCalendar cal) const;
%MethodCode
        // QStringView has issues being implemented as a mapped type.
        sipRes = new QString(sipCpp->toString(*a0, QStringView(*a1), *a2));
%End

%End
    QString toString(const QDate &date, QLocale::FormatType format = QLocale::LongFormat) const;
%If (Qt_5_14_0 -)
    QString toString(const QDate &date, QLocale::FormatType format, QCalendar cal) const;
%End
    QString toString(const QTime &time, const QString &formatStr) const;
    QString toString(const QTime &time, QLocale::FormatType format = QLocale::LongFormat) const;
    QString dateFormat(QLocale::FormatType format = QLocale::LongFormat) const;
    QString timeFormat(QLocale::FormatType format = QLocale::LongFormat) const;
    QString dateTimeFormat(QLocale::FormatType format = QLocale::LongFormat) const;
    QDate toDate(const QString &string, QLocale::FormatType format = QLocale::LongFormat) const;
    QDate toDate(const QString &string, const QString &format) const;
    QTime toTime(const QString &string, QLocale::FormatType format = QLocale::LongFormat) const;
    QTime toTime(const QString &string, const QString &format) const;
    QDateTime toDateTime(const QString &string, QLocale::FormatType format = QLocale::LongFormat) const;
    QDateTime toDateTime(const QString &string, const QString &format) const;
    QChar decimalPoint() const;
    QChar groupSeparator() const;
    QChar percent() const;
    QChar zeroDigit() const;
    QChar negativeSign() const;
    QChar exponential() const;
    QString monthName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    QString dayName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    void setNumberOptions(QLocale::NumberOptions options);
    QLocale::NumberOptions numberOptions() const;

    enum MeasurementSystem
    {
        MetricSystem,
        ImperialSystem,
        ImperialUSSystem,
        ImperialUKSystem,
    };

    QLocale::MeasurementSystem measurementSystem() const;
    QChar positiveSign() const;
    QString standaloneMonthName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    QString standaloneDayName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    QString amText() const;
    QString pmText() const;
    Qt::LayoutDirection textDirection() const;

    enum Script
    {
        AnyScript,
        ArabicScript,
        CyrillicScript,
        DeseretScript,
        GurmukhiScript,
        SimplifiedHanScript,
        TraditionalHanScript,
        LatinScript,
        MongolianScript,
        TifinaghScript,
        SimplifiedChineseScript,
        TraditionalChineseScript,
        ArmenianScript,
        BengaliScript,
        CherokeeScript,
        DevanagariScript,
        EthiopicScript,
        GeorgianScript,
        GreekScript,
        GujaratiScript,
        HebrewScript,
        JapaneseScript,
        KhmerScript,
        KannadaScript,
        KoreanScript,
        LaoScript,
        MalayalamScript,
        MyanmarScript,
        OriyaScript,
        TamilScript,
        TeluguScript,
        ThaanaScript,
        ThaiScript,
        TibetanScript,
        SinhalaScript,
        SyriacScript,
        YiScript,
        VaiScript,
%If (Qt_5_1_0 -)
        AvestanScript,
%End
%If (Qt_5_1_0 -)
        BalineseScript,
%End
%If (Qt_5_1_0 -)
        BamumScript,
%End
%If (Qt_5_1_0 -)
        BatakScript,
%End
%If (Qt_5_1_0 -)
        BopomofoScript,
%End
%If (Qt_5_1_0 -)
        BrahmiScript,
%End
%If (Qt_5_1_0 -)
        BugineseScript,
%End
%If (Qt_5_1_0 -)
        BuhidScript,
%End
%If (Qt_5_1_0 -)
        CanadianAboriginalScript,
%End
%If (Qt_5_1_0 -)
        CarianScript,
%End
%If (Qt_5_1_0 -)
        ChakmaScript,
%End
%If (Qt_5_1_0 -)
        ChamScript,
%End
%If (Qt_5_1_0 -)
        CopticScript,
%End
%If (Qt_5_1_0 -)
        CypriotScript,
%End
%If (Qt_5_1_0 -)
        EgyptianHieroglyphsScript,
%End
%If (Qt_5_1_0 -)
        FraserScript,
%End
%If (Qt_5_1_0 -)
        GlagoliticScript,
%End
%If (Qt_5_1_0 -)
        GothicScript,
%End
%If (Qt_5_1_0 -)
        HanScript,
%End
%If (Qt_5_1_0 -)
        HangulScript,
%End
%If (Qt_5_1_0 -)
        HanunooScript,
%End
%If (Qt_5_1_0 -)
        ImperialAramaicScript,
%End
%If (Qt_5_1_0 -)
        InscriptionalPahlaviScript,
%End
%If (Qt_5_1_0 -)
        InscriptionalParthianScript,
%End
%If (Qt_5_1_0 -)
        JavaneseScript,
%End
%If (Qt_5_1_0 -)
        KaithiScript,
%End
%If (Qt_5_1_0 -)
        KatakanaScript,
%End
%If (Qt_5_1_0 -)
        KayahLiScript,
%End
%If (Qt_5_1_0 -)
        KharoshthiScript,
%End
%If (Qt_5_1_0 -)
        LannaScript,
%End
%If (Qt_5_1_0 -)
        LepchaScript,
%End
%If (Qt_5_1_0 -)
        LimbuScript,
%End
%If (Qt_5_1_0 -)
        LinearBScript,
%End
%If (Qt_5_1_0 -)
        LycianScript,
%End
%If (Qt_5_1_0 -)
        LydianScript,
%End
%If (Qt_5_1_0 -)
        MandaeanScript,
%End
%If (Qt_5_1_0 -)
        MeiteiMayekScript,
%End
%If (Qt_5_1_0 -)
        MeroiticScript,
%End
%If (Qt_5_1_0 -)
        MeroiticCursiveScript,
%End
%If (Qt_5_1_0 -)
        NkoScript,
%End
%If (Qt_5_1_0 -)
        NewTaiLueScript,
%End
%If (Qt_5_1_0 -)
        OghamScript,
%End
%If (Qt_5_1_0 -)
        OlChikiScript,
%End
%If (Qt_5_1_0 -)
        OldItalicScript,
%End
%If (Qt_5_1_0 -)
        OldPersianScript,
%End
%If (Qt_5_1_0 -)
        OldSouthArabianScript,
%End
%If (Qt_5_1_0 -)
        OrkhonScript,
%End
%If (Qt_5_1_0 -)
        OsmanyaScript,
%End
%If (Qt_5_1_0 -)
        PhagsPaScript,
%End
%If (Qt_5_1_0 -)
        PhoenicianScript,
%End
%If (Qt_5_1_0 -)
        PollardPhoneticScript,
%End
%If (Qt_5_1_0 -)
        RejangScript,
%End
%If (Qt_5_1_0 -)
        RunicScript,
%End
%If (Qt_5_1_0 -)
        SamaritanScript,
%End
%If (Qt_5_1_0 -)
        SaurashtraScript,
%End
%If (Qt_5_1_0 -)
        SharadaScript,
%End
%If (Qt_5_1_0 -)
        ShavianScript,
%End
%If (Qt_5_1_0 -)
        SoraSompengScript,
%End
%If (Qt_5_1_0 -)
        CuneiformScript,
%End
%If (Qt_5_1_0 -)
        SundaneseScript,
%End
%If (Qt_5_1_0 -)
        SylotiNagriScript,
%End
%If (Qt_5_1_0 -)
        TagalogScript,
%End
%If (Qt_5_1_0 -)
        TagbanwaScript,
%End
%If (Qt_5_1_0 -)
        TaiLeScript,
%End
%If (Qt_5_1_0 -)
        TaiVietScript,
%End
%If (Qt_5_1_0 -)
        TakriScript,
%End
%If (Qt_5_1_0 -)
        UgariticScript,
%End
%If (Qt_5_1_0 -)
        BrailleScript,
%End
%If (Qt_5_1_0 -)
        HiraganaScript,
%End
%If (Qt_5_5_0 -)
        CaucasianAlbanianScript,
%End
%If (Qt_5_5_0 -)
        BassaVahScript,
%End
%If (Qt_5_5_0 -)
        DuployanScript,
%End
%If (Qt_5_5_0 -)
        ElbasanScript,
%End
%If (Qt_5_5_0 -)
        GranthaScript,
%End
%If (Qt_5_5_0 -)
        PahawhHmongScript,
%End
%If (Qt_5_5_0 -)
        KhojkiScript,
%End
%If (Qt_5_5_0 -)
        LinearAScript,
%End
%If (Qt_5_5_0 -)
        MahajaniScript,
%End
%If (Qt_5_5_0 -)
        ManichaeanScript,
%End
%If (Qt_5_5_0 -)
        MendeKikakuiScript,
%End
%If (Qt_5_5_0 -)
        ModiScript,
%End
%If (Qt_5_5_0 -)
        MroScript,
%End
%If (Qt_5_5_0 -)
        OldNorthArabianScript,
%End
%If (Qt_5_5_0 -)
        NabataeanScript,
%End
%If (Qt_5_5_0 -)
        PalmyreneScript,
%End
%If (Qt_5_5_0 -)
        PauCinHauScript,
%End
%If (Qt_5_5_0 -)
        OldPermicScript,
%End
%If (Qt_5_5_0 -)
        PsalterPahlaviScript,
%End
%If (Qt_5_5_0 -)
        SiddhamScript,
%End
%If (Qt_5_5_0 -)
        KhudawadiScript,
%End
%If (Qt_5_5_0 -)
        TirhutaScript,
%End
%If (Qt_5_5_0 -)
        VarangKshitiScript,
%End
%If (Qt_5_7_0 -)
        AhomScript,
%End
%If (Qt_5_7_0 -)
        AnatolianHieroglyphsScript,
%End
%If (Qt_5_7_0 -)
        HatranScript,
%End
%If (Qt_5_7_0 -)
        MultaniScript,
%End
%If (Qt_5_7_0 -)
        OldHungarianScript,
%End
%If (Qt_5_7_0 -)
        SignWritingScript,
%End
%If (Qt_5_7_0 -)
        AdlamScript,
%End
%If (Qt_5_7_0 -)
        BhaiksukiScript,
%End
%If (Qt_5_7_0 -)
        MarchenScript,
%End
%If (Qt_5_7_0 -)
        NewaScript,
%End
%If (Qt_5_7_0 -)
        OsageScript,
%End
%If (Qt_5_7_0 -)
        TangutScript,
%End
%If (Qt_5_7_0 -)
        HanWithBopomofoScript,
%End
%If (Qt_5_7_0 -)
        JamoScript,
%End
    };

    enum CurrencySymbolFormat
    {
        CurrencyIsoCode,
        CurrencySymbol,
        CurrencyDisplayName,
    };

    QLocale(QLocale::Language language, QLocale::Script script, QLocale::Country country);
    QLocale::Script script() const;
    QString bcp47Name() const;
    QString nativeLanguageName() const;
    QString nativeCountryName() const;
    Qt::DayOfWeek firstDayOfWeek() const;
    QList<Qt::DayOfWeek> weekdays() const;
    QString toUpper(const QString &str) const;
    QString toLower(const QString &str) const;
    QString currencySymbol(QLocale::CurrencySymbolFormat format = QLocale::CurrencySymbol) const;
    QString toCurrencyString(double value /Constrained/, const QString &symbol = QString()) const;
%If (Qt_5_7_0 -)
    QString toCurrencyString(double value /Constrained/, const QString &symbol, int precision) const;
%End
    QStringList uiLanguages() const;
    static QString scriptToString(QLocale::Script script);
    static QList<QLocale> matchingLocales(QLocale::Language language, QLocale::Script script, QLocale::Country country);

    enum QuotationStyle
    {
        StandardQuotation,
        AlternateQuotation,
    };

    QString quoteString(const QString &str, QLocale::QuotationStyle style = QLocale::StandardQuotation) const;
    QString createSeparatedList(const QStringList &list) const;
%If (Qt_5_6_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
%If (Qt_5_7_0 -)

    enum FloatingPointPrecisionOption
    {
        FloatingPointShortest,
    };

%End
%If (Qt_5_7_0 -)
    void swap(QLocale &other /Constrained/);
%End
    QString toString(SIP_PYOBJECT i /TypeHint="int"/) const;
%MethodCode
        // Convert a Python int avoiding overflow as much as possible.
        
        static PyObject *zero = 0;
        if (!zero)
            zero = PyLong_FromLong(0);
        
        int rc = PyObject_RichCompareBool(a0, zero, Py_LT);
        
        PyErr_Clear();
        
        if (rc < 0)
        {
            sipError = sipBadCallableArg(0, a0);
        }
        else if (rc)
        {
        #if defined(HAVE_LONG_LONG)
            PY_LONG_LONG value = PyLong_AsLongLong(a0);
        #else
            long value = PyLong_AsLong(a0);
        #endif
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toString(value));
            }
        }
        else
        {
        #if PY_MAJOR_VERSION >= 3
        #if defined(HAVE_LONG_LONG)
            unsigned PY_LONG_LONG value = PyLong_AsUnsignedLongLongMask(a0);
        #else
            unsigned long value = PyLong_AsUnsignedLongMask(a0);
        #endif
        #else
        #if defined(HAVE_LONG_LONG)
            unsigned PY_LONG_LONG value = PyInt_AsUnsignedLongLongMask(a0);
        #else
            unsigned long value = PyInt_AsUnsignedLongMask(a0);
        #endif
        #endif
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toString(value));
            }
        }
%End

    QString toCurrencyString(SIP_PYOBJECT value /TypeHint="int"/, const QString &symbol = QString()) const;
%MethodCode
        // Convert a Python int avoiding overflow as much as possible.
        
        static PyObject *zero = 0;
        if (!zero)
            zero = PyLong_FromLong(0);
        
        int rc = PyObject_RichCompareBool(a0, zero, Py_LT);
        
        PyErr_Clear();
        
        if (rc < 0)
        {
            sipError = sipBadCallableArg(0, a0);
        }
        else if (rc)
        {
        #if defined(HAVE_LONG_LONG)
            PY_LONG_LONG value = PyLong_AsLongLong(a0);
        #else
            long value = PyLong_AsLong(a0);
        #endif
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toCurrencyString(value, *a1));
            }
        }
        else
        {
        #if defined(HAVE_LONG_LONG)
            unsigned PY_LONG_LONG value = PyLong_AsUnsignedLongLongMask(a0);
        #else
            unsigned long value = PyLong_AsUnsignedLongMask(a0);
        #endif
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toCurrencyString(value, *a1));
            }
        }
%End

%If (Qt_5_10_0 -)

    enum DataSizeFormat
    {
        DataSizeIecFormat,
        DataSizeTraditionalFormat,
        DataSizeSIFormat,
    };

%End
%If (Qt_5_10_0 -)
    typedef QFlags<QLocale::DataSizeFormat> DataSizeFormats;
%End
%If (Qt_5_10_0 -)
    QString formattedDataSize(qint64 bytes, int precision = 2, QLocale::DataSizeFormats format = QLocale::DataSizeIecFormat);
%End
%If (Qt_5_13_0 -)
    long toLong(const QString &s, bool *ok = 0) const;
%End
%If (Qt_5_13_0 -)
    ulong toULong(const QString &s, bool *ok = 0) const;
%End
%If (Qt_5_14_0 -)
    QDate toDate(const QString &string, QLocale::FormatType format, QCalendar cal) const;
%End
%If (Qt_5_14_0 -)
    QTime toTime(const QString &string, QLocale::FormatType format, QCalendar cal) const;
%End
%If (Qt_5_14_0 -)
    QDateTime toDateTime(const QString &string, QLocale::FormatType format, QCalendar cal) const;
%End
%If (Qt_5_14_0 -)
    QDate toDate(const QString &string, const QString &format, QCalendar cal) const;
%End
%If (Qt_5_14_0 -)
    QTime toTime(const QString &string, const QString &format, QCalendar cal) const;
%End
%If (Qt_5_14_0 -)
    QDateTime toDateTime(const QString &string, const QString &format, QCalendar cal) const;
%End
%If (Qt_5_14_0 -)
    QLocale collation() const;
%End
};

QDataStream &operator<<(QDataStream &, const QLocale & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QLocale & /Constrained/) /ReleaseGIL/;
QFlags<QLocale::NumberOption> operator|(QLocale::NumberOption f1, QFlags<QLocale::NumberOption> f2);
