// qsemaphore.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSemaphore
{
%TypeHeaderCode
#include <qsemaphore.h>
%End

public:
    explicit QSemaphore(int n = 0);
    ~QSemaphore();
    void acquire(int n = 1) /ReleaseGIL/;
    bool tryAcquire(int n = 1);
    bool tryAcquire(int n, int timeout) /ReleaseGIL/;
    void release(int n = 1);
    int available() const;

private:
    QSemaphore(const QSemaphore &);
};

%If (Qt_5_10_0 -)

class QSemaphoreReleaser /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsemaphore.h>
%End

public:
    QSemaphoreReleaser();
    QSemaphoreReleaser(QSemaphore *sem, int n = 1);
    ~QSemaphoreReleaser();
    void swap(QSemaphoreReleaser &other);
    QSemaphore *semaphore() const;
    QSemaphore *cancel();
};

%End
