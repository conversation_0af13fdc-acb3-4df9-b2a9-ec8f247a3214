// qpoint.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPoint
{
%TypeHeaderCode
#include <qpoint.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"ii", sipCpp->x(), sipCpp->y());
%End

public:
    int manhattanLength() const;
    QPoint();
    QPoint(int xpos, int ypos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QPoint()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QPoint()");
        #endif
        }
        else
        {
            sipRes =
        #if PY_MAJOR_VERSION >= 3
                PyUnicode_FromFormat
        #else
                PyString_FromFormat
        #endif
                    ("PyQt5.QtCore.QPoint(%i, %i)", sipCpp->x(), sipCpp->y());
        }
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    int x() const;
    int y() const;
    void setX(int xpos);
    void setY(int ypos);
    QPoint &operator+=(const QPoint &p);
    QPoint &operator-=(const QPoint &p);
    QPoint &operator*=(int c /Constrained/);
    QPoint &operator*=(double c);
    QPoint &operator/=(qreal c);
%If (Qt_5_1_0 -)
    static int dotProduct(const QPoint &p1, const QPoint &p2);
%End
%If (Qt_5_14_0 -)
    QPoint transposed() const;
%End
};

QDataStream &operator<<(QDataStream &, const QPoint & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPoint & /Constrained/) /ReleaseGIL/;
bool operator==(const QPoint &p1, const QPoint &p2);
bool operator!=(const QPoint &p1, const QPoint &p2);
const QPoint operator+(const QPoint &p1, const QPoint &p2);
const QPoint operator-(const QPoint &p1, const QPoint &p2);
const QPoint operator*(const QPoint &p, int c /Constrained/);
const QPoint operator*(int c /Constrained/, const QPoint &p);
const QPoint operator*(const QPoint &p, double c);
const QPoint operator*(double c, const QPoint &p);
const QPoint operator-(const QPoint &p);
const QPoint operator/(const QPoint &p, qreal c);

class QPointF /TypeHintIn="Union[QPointF, QPoint]"/
{
%TypeHeaderCode
#include <qpoint.h>
%End

%ConvertToTypeCode
// Allow a QPoint whenever a QPointF is expected.  This is mainly to help source
// compatibility for Qt5.

if (sipIsErr == NULL)
    return (sipCanConvertToType(sipPy, sipType_QPointF, SIP_NO_CONVERTORS) ||
            sipCanConvertToType(sipPy, sipType_QPoint, 0));

if (sipCanConvertToType(sipPy, sipType_QPointF, SIP_NO_CONVERTORS))
{
    *sipCppPtr = reinterpret_cast<QPointF *>(sipConvertToType(sipPy, sipType_QPointF, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

    return 0;
}

int state;

QPoint *pt = reinterpret_cast<QPoint *>(sipConvertToType(sipPy, sipType_QPoint, 0, 0, &state, sipIsErr));

if (*sipIsErr)
{
    sipReleaseType(pt, sipType_QPoint, state);
    return 0;
}

*sipCppPtr = new QPointF(*pt);

sipReleaseType(pt, sipType_QPoint, state);

return sipGetState(sipTransferObj);
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"dd", sipCpp->x(), sipCpp->y());
%End

public:
    QPointF();
    QPointF(qreal xpos, qreal ypos);
    QPointF(const QPoint &p);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QPointF()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QPointF()");
        #endif
        }
        else
        {
            PyObject *x = PyFloat_FromDouble(sipCpp->x());
            PyObject *y = PyFloat_FromDouble(sipCpp->y());
        
            if (x && y)
            {
        #if PY_MAJOR_VERSION >= 3
                sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QPointF(%R, %R)", x, y);
        #else
                sipRes = PyString_FromString("PyQt5.QtCore.QPointF(");
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(x));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(y));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
            }
        
            Py_XDECREF(x);
            Py_XDECREF(y);
        }
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    qreal x() const;
    qreal y() const;
    void setX(qreal xpos);
    void setY(qreal ypos);
    QPointF &operator+=(const QPointF &p);
    QPointF &operator-=(const QPointF &p);
    QPointF &operator*=(qreal c);
    QPointF &operator/=(qreal c);
    QPoint toPoint() const;
    qreal manhattanLength() const;
%If (Qt_5_1_0 -)
    static qreal dotProduct(const QPointF &p1, const QPointF &p2);
%End
%If (Qt_5_14_0 -)
    QPointF transposed() const;
%End
};

QDataStream &operator<<(QDataStream &, const QPointF & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPointF & /Constrained/) /ReleaseGIL/;
bool operator==(const QPointF &p1, const QPointF &p2);
bool operator!=(const QPointF &p1, const QPointF &p2);
const QPointF operator+(const QPointF &p1, const QPointF &p2);
const QPointF operator-(const QPointF &p1, const QPointF &p2);
const QPointF operator*(const QPointF &p, qreal c);
const QPointF operator*(qreal c, const QPointF &p);
const QPointF operator-(const QPointF &p);
const QPointF operator/(const QPointF &p, qreal c);
const QPoint operator+(const QPoint &p);
const QPointF operator+(const QPointF &p);
