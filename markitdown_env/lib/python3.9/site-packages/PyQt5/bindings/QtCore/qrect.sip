// qrect.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRect
{
%TypeHeaderCode
#include <qrect.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"iiii", sipCpp->x(), sipCpp->y(), sipCpp->width(), sipCpp->height());
%End

public:
    QRect();
    QRect normalized() const;
    void moveCenter(const QPoint &p);
    QRect operator|(const QRect &r) const;
    QRect operator&(const QRect &r) const;
    bool contains(const QPoint &point, bool proper = false) const;
    int __contains__(const QPoint &p) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    bool contains(const QRect &rectangle, bool proper = false) const;
    int __contains__(const QRect &r) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    bool intersects(const QRect &r) const;
    QRect(int aleft, int atop, int awidth, int aheight);
    QRect(const QPoint &atopLeft, const QPoint &abottomRight);
    QRect(const QPoint &atopLeft, const QSize &asize);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QRect()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QRect()");
        #endif
        }
        else
        {
            sipRes =
        #if PY_MAJOR_VERSION >= 3
                PyUnicode_FromFormat
        #else
                PyString_FromFormat
        #endif
                    ("PyQt5.QtCore.QRect(%i, %i, %i, %i)", sipCpp->left(),
                    sipCpp->top(), sipCpp->width(), sipCpp->height());
        }
%End

    bool isNull() const;
    bool isEmpty() const;
    bool isValid() const;
    int __bool__() const;
%MethodCode
        sipRes = sipCpp->isValid();
%End

    int left() const;
    int top() const;
    int right() const;
    int bottom() const;
    int x() const;
    int y() const;
    void setLeft(int pos);
    void setTop(int pos);
    void setRight(int pos);
    void setBottom(int pos);
    void setTopLeft(const QPoint &p);
    void setBottomRight(const QPoint &p);
    void setTopRight(const QPoint &p);
    void setBottomLeft(const QPoint &p);
    void setX(int ax);
    void setY(int ay);
    QPoint topLeft() const;
    QPoint bottomRight() const;
    QPoint topRight() const;
    QPoint bottomLeft() const;
    QPoint center() const;
    int width() const;
    int height() const;
    QSize size() const;
    void translate(int dx, int dy);
    void translate(const QPoint &p);
    QRect translated(int dx, int dy) const;
    QRect translated(const QPoint &p) const;
    void moveTo(int ax, int ay);
    void moveTo(const QPoint &p);
    void moveLeft(int pos);
    void moveTop(int pos);
    void moveRight(int pos);
    void moveBottom(int pos);
    void moveTopLeft(const QPoint &p);
    void moveBottomRight(const QPoint &p);
    void moveTopRight(const QPoint &p);
    void moveBottomLeft(const QPoint &p);
    void getRect(int *ax, int *ay, int *aw, int *ah) const;
    void setRect(int ax, int ay, int aw, int ah);
    void getCoords(int *xp1, int *yp1, int *xp2, int *yp2) const;
    void setCoords(int xp1, int yp1, int xp2, int yp2);
    QRect adjusted(int xp1, int yp1, int xp2, int yp2) const;
    void adjust(int dx1, int dy1, int dx2, int dy2);
    void setWidth(int w);
    void setHeight(int h);
    void setSize(const QSize &s);
    bool contains(int ax, int ay, bool aproper) const;
    bool contains(int ax, int ay) const;
    QRect &operator|=(const QRect &r);
    QRect &operator&=(const QRect &r);
    QRect intersected(const QRect &other) const;
    QRect united(const QRect &r) const;
%If (Qt_5_1_0 -)
    QRect marginsAdded(const QMargins &margins) const;
%End
%If (Qt_5_1_0 -)
    QRect marginsRemoved(const QMargins &margins) const;
%End
%If (Qt_5_1_0 -)
    QRect &operator+=(const QMargins &margins);
%End
%If (Qt_5_1_0 -)
    QRect &operator-=(const QMargins &margins);
%End
%If (Qt_5_7_0 -)
    QRect transposed() const;
%End
};

QDataStream &operator<<(QDataStream &, const QRect & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QRect & /Constrained/) /ReleaseGIL/;
bool operator==(const QRect &r1, const QRect &r2);
bool operator!=(const QRect &r1, const QRect &r2);

class QRectF
{
%TypeHeaderCode
#include <qrect.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"dddd", sipCpp->x(), sipCpp->y(), sipCpp->width(), sipCpp->height());
%End

public:
    QRectF();
    QRectF(const QPointF &atopLeft, const QSizeF &asize);
    QRectF(const QPointF &atopLeft, const QPointF &abottomRight);
    QRectF(qreal aleft, qreal atop, qreal awidth, qreal aheight);
    QRectF(const QRect &r);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QRectF()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QRectF()");
        #endif
        }
        else
        {
            PyObject *l = PyFloat_FromDouble(sipCpp->left());
            PyObject *t = PyFloat_FromDouble(sipCpp->top());
            PyObject *w = PyFloat_FromDouble(sipCpp->width());
            PyObject *h = PyFloat_FromDouble(sipCpp->height());
        
            if (l && t && w && h)
            {
        #if PY_MAJOR_VERSION >= 3
                sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QRectF(%R, %R, %R, %R)", l,
                        t, w, h);
        #else
                sipRes = PyString_FromString("PyQt5.QtCore.QRectF(");
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(l));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(t));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(w));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(h));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
            }
        
            Py_XDECREF(l);
            Py_XDECREF(t);
            Py_XDECREF(w);
            Py_XDECREF(h);
        }
%End

    QRectF normalized() const;
    qreal left() const;
    qreal top() const;
    qreal right() const;
    qreal bottom() const;
    void setX(qreal pos);
    void setY(qreal pos);
    QPointF topLeft() const;
    QPointF bottomRight() const;
    QPointF topRight() const;
    QPointF bottomLeft() const;
    QRectF operator|(const QRectF &r) const;
    QRectF operator&(const QRectF &r) const;
    bool contains(const QPointF &p) const;
    int __contains__(const QPointF &p) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    bool contains(const QRectF &r) const;
    int __contains__(const QRectF &r) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    bool intersects(const QRectF &r) const;
    bool isNull() const;
    bool isEmpty() const;
    bool isValid() const;
    int __bool__() const;
%MethodCode
        sipRes = sipCpp->isValid();
%End

    qreal x() const;
    qreal y() const;
    void setLeft(qreal pos);
    void setRight(qreal pos);
    void setTop(qreal pos);
    void setBottom(qreal pos);
    void setTopLeft(const QPointF &p);
    void setTopRight(const QPointF &p);
    void setBottomLeft(const QPointF &p);
    void setBottomRight(const QPointF &p);
    QPointF center() const;
    void moveLeft(qreal pos);
    void moveTop(qreal pos);
    void moveRight(qreal pos);
    void moveBottom(qreal pos);
    void moveTopLeft(const QPointF &p);
    void moveTopRight(const QPointF &p);
    void moveBottomLeft(const QPointF &p);
    void moveBottomRight(const QPointF &p);
    void moveCenter(const QPointF &p);
    qreal width() const;
    qreal height() const;
    QSizeF size() const;
    void translate(qreal dx, qreal dy);
    void translate(const QPointF &p);
    void moveTo(qreal ax, qreal ay);
    void moveTo(const QPointF &p);
    QRectF translated(qreal dx, qreal dy) const;
    QRectF translated(const QPointF &p) const;
    void getRect(qreal *ax, qreal *ay, qreal *aaw, qreal *aah) const;
    void setRect(qreal ax, qreal ay, qreal aaw, qreal aah);
    void getCoords(qreal *xp1, qreal *yp1, qreal *xp2, qreal *yp2) const;
    void setCoords(qreal xp1, qreal yp1, qreal xp2, qreal yp2);
    void adjust(qreal xp1, qreal yp1, qreal xp2, qreal yp2);
    QRectF adjusted(qreal xp1, qreal yp1, qreal xp2, qreal yp2) const;
    void setWidth(qreal aw);
    void setHeight(qreal ah);
    void setSize(const QSizeF &s);
    bool contains(qreal ax, qreal ay) const;
    QRectF &operator|=(const QRectF &r);
    QRectF &operator&=(const QRectF &r);
    QRectF intersected(const QRectF &r) const;
    QRectF united(const QRectF &r) const;
    QRect toAlignedRect() const;
    QRect toRect() const;
%If (Qt_5_3_0 -)
    QRectF marginsAdded(const QMarginsF &margins) const;
%End
%If (Qt_5_3_0 -)
    QRectF marginsRemoved(const QMarginsF &margins) const;
%End
%If (Qt_5_3_0 -)
    QRectF &operator+=(const QMarginsF &margins);
%End
%If (Qt_5_3_0 -)
    QRectF &operator-=(const QMarginsF &margins);
%End
%If (Qt_5_7_0 -)
    QRectF transposed() const;
%End
};

QDataStream &operator<<(QDataStream &, const QRectF & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QRectF & /Constrained/) /ReleaseGIL/;
bool operator==(const QRectF &r1, const QRectF &r2);
bool operator!=(const QRectF &r1, const QRectF &r2);
%If (Qt_5_3_0 -)
QRect operator+(const QRect &rectangle, const QMargins &margins);
%End
%If (Qt_5_3_0 -)
QRect operator+(const QMargins &margins, const QRect &rectangle);
%End
%If (Qt_5_3_0 -)
QRect operator-(const QRect &lhs, const QMargins &rhs);
%End
%If (Qt_5_3_0 -)
QRectF operator+(const QRectF &lhs, const QMarginsF &rhs);
%End
%If (Qt_5_3_0 -)
QRectF operator+(const QMarginsF &lhs, const QRectF &rhs);
%End
%If (Qt_5_3_0 -)
QRectF operator-(const QRectF &lhs, const QMarginsF &rhs);
%End
