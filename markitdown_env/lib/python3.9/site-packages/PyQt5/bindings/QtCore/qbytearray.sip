// qbytearray.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qbytearray.h>
%End

class QByteArray /TypeHintIn="Union[QByteArray, bytes, bytearray]"/
{
%TypeHeaderCode
#include <qbytearray.h>
%End

%TypeCode
// This is needed by __hash__().
#include <qhash.h>


// Convenience function for converting a QByteArray to a Python str object.
static PyObject *QByteArrayToPyStr(QByteArray *ba)
{
    char *data = ba->data();

    if (data)
        // QByteArrays may have embedded '\0's so set the size explicitly.    
        return SIPBytes_FromStringAndSize(data, ba->size());

    return SIPBytes_FromString("");
}
%End

%ConvertToTypeCode
// We have to be very careful about what we allow to be converted to a
// QByteArray and to a QString as we need to take into account the v1 and v2
// APIs and Python v2.x and v3.x.
//
// QSvgRenderer() is a good example of what needs to work "naturally".  This
// has a ctor that takes a QString argument that is the name of the SVG file.
// It has another ctor that takes a QByteArray argument that is the SVG data.
//
// In Python v2.x we want a str object to be interpreted as the name of the
// file (as that is the historical behaviour).  This has the following
// implications.
//
// - The QString version of the ctor must appear before the QByteArray version
//   in the .sip file.  This rule should be applied wherever a similar
//   situation arises.
// - A QString must not automatically convert a QByteArray.
// - QByteArray must also exist in the v2 API.
//
// In Python v3.x we want a bytes object to be used wherever a QByteArray is
// expected.  This means that a QString must not automatically convert a bytes
// object.
//
// In PyQt v5.4 and earlier a QByteArray could be created from a Latin-1
// encoded string.  This was a mistaken attempt to ease the porting of Python2
// code to Python3.

if (sipIsErr == NULL)
    return (PyByteArray_Check(sipPy) || SIPBytes_Check(sipPy) ||
            sipCanConvertToType(sipPy, sipType_QByteArray, SIP_NO_CONVERTORS));

if (PyByteArray_Check(sipPy))
{
    *sipCppPtr = new QByteArray(PyByteArray_AsString(sipPy),
            PyByteArray_Size(sipPy));

    return sipGetState(sipTransferObj);
}

if (SIPBytes_Check(sipPy))
{
    *sipCppPtr = new QByteArray(SIPBytes_AsString(sipPy),
            SIPBytes_Size(sipPy));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QByteArray *>(sipConvertToType(sipPy,
        sipType_QByteArray, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%BIGetBufferCode
    #if defined(Py_LIMITED_API)
        Q_UNUSED(sipSelf);
    
        sipBuffer->bd_buffer = sipCpp->data();
        sipBuffer->bd_length = sipCpp->size();
        sipBuffer->bd_readonly = 0;
        sipRes = 0;
    #else
        sipRes = PyBuffer_FillInfo(sipBuffer, sipSelf, sipCpp->data(),
                sipCpp->size(), 0, sipFlags);
    #endif
%End

%BIGetReadBufferCode
    if (sipSegment != 0)
    {
        PyErr_SetString(PyExc_SystemError, "accessing non-existent QByteArray segment");
        sipRes = -1;
    }
    else
    {
        *sipPtrPtr = (void *)sipCpp->data();
        sipRes = sipCpp->size();
    }
%End

%BIGetSegCountCode
    if (sipLenPtr)
        *sipLenPtr = sipCpp->size();
    
    sipRes = 1;
%End

%BIGetCharBufferCode
    if (sipSegment != 0)
    {
        PyErr_SetString(PyExc_SystemError, "accessing non-existent QByteArray segment");
        sipRes = -1;
    }
    else
    {
        *sipPtrPtr = (void *)sipCpp->data();
        sipRes = sipCpp->size();
    }
%End

%PickleCode
    #if PY_MAJOR_VERSION >= 3
    sipRes = Py_BuildValue((char *)"(y#)", sipCpp->data(), static_cast<Py_ssize_t>(sipCpp->size()));
    #else
    sipRes = Py_BuildValue((char *)"(s#)", sipCpp->data(), static_cast<Py_ssize_t>(sipCpp->size()));
    #endif
%End

public:
    QByteArray();
    QByteArray(int size, char c);
    QByteArray(const QByteArray &a);
    ~QByteArray();
    void resize(int size);
    QByteArray &fill(char ch, int size = -1);
    void clear();
    int indexOf(const QByteArray &ba, int from = 0) const;
    int indexOf(const QString &str, int from = 0) const;
    int lastIndexOf(const QByteArray &ba, int from = -1) const;
    int lastIndexOf(const QString &str, int from = -1) const;
    int count(const QByteArray &a) const;
    QByteArray left(int len) const;
    QByteArray right(int len) const;
    QByteArray mid(int pos, int length = -1) const;
    bool startsWith(const QByteArray &a) const;
    bool endsWith(const QByteArray &a) const;
    void truncate(int pos);
    void chop(int n);
    QByteArray toLower() const;
    QByteArray toUpper() const;
    QByteArray trimmed() const;
    QByteArray simplified() const;
    QByteArray leftJustified(int width, char fill = ' ', bool truncate = false) const;
    QByteArray rightJustified(int width, char fill = ' ', bool truncate = false) const;
    QByteArray &prepend(const QByteArray &a);
    QByteArray &append(const QByteArray &a);
    QByteArray &append(const QString &s);
    QByteArray &insert(int i, const QByteArray &a);
    QByteArray &insert(int i, const QString &s);
    QByteArray &remove(int index, int len);
    QByteArray &replace(int index, int len, const QByteArray &s);
    QByteArray &replace(const QByteArray &before, const QByteArray &after);
    QByteArray &replace(const QString &before, const QByteArray &after);
    QList<QByteArray> split(char sep) const;
    QByteArray &operator+=(const QByteArray &a);
    QByteArray &operator+=(const QString &s);
    bool operator==(const QString &s2) const;
    bool operator!=(const QString &s2) const;
    bool operator<(const QString &s2) const;
    bool operator>(const QString &s2) const;
    bool operator<=(const QString &s2) const;
    bool operator>=(const QString &s2) const;
    short toShort(bool *ok = 0, int base = 10) const;
    ushort toUShort(bool *ok = 0, int base = 10) const;
    int toInt(bool *ok = 0, int base = 10) const;
    uint toUInt(bool *ok = 0, int base = 10) const;
    long toLong(bool *ok = 0, int base = 10) const;
    ulong toULong(bool *ok = 0, int base = 10) const;
    qlonglong toLongLong(bool *ok = 0, int base = 10) const;
    qulonglong toULongLong(bool *ok = 0, int base = 10) const;
    float toFloat(bool *ok = 0) const;
    double toDouble(bool *ok = 0) const;
    QByteArray toBase64() const;
    QByteArray &setNum(double n /Constrained/, char format = 'g', int precision = 6);
    QByteArray &setNum(SIP_PYOBJECT n /TypeHint="int"/, int base = 10);
%MethodCode
        #if PY_MAJOR_VERSION < 3
        if (PyInt_Check(a0))
        {
            qlonglong val = PyInt_AsLong(a0);
        
            sipRes = &sipCpp->setNum(val, a1);
        }
        else
        #endif
        {
            qlonglong val = sipLong_AsLongLong(a0);
        
            if (!PyErr_Occurred())
            {
                sipRes = &sipCpp->setNum(val, a1);
            }
            else
            {
                // If it is positive then it might fit an unsigned long long.
        
                qulonglong uval = sipLong_AsUnsignedLongLong(a0);
        
                if (!PyErr_Occurred())
                {
                    sipRes = &sipCpp->setNum(uval, a1);
                }
                else
                {
                    sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                            ? sipErrorFail : sipErrorContinue);
                }
            }
        }
%End

    static QByteArray number(double n /Constrained/, char format = 'g', int precision = 6);
    static QByteArray number(SIP_PYOBJECT n /TypeHint="int"/, int base = 10);
%MethodCode
        #if PY_MAJOR_VERSION < 3
        if (PyInt_Check(a0))
        {
            qlonglong val = PyInt_AsLong(a0);
        
            sipRes = new QByteArray(QByteArray::number(val, a1));
        }
        else
        #endif
        {
            qlonglong val = sipLong_AsLongLong(a0);
        
            if (!PyErr_Occurred())
            {
                sipRes = new QByteArray(QByteArray::number(val, a1));
            }
            else
            {
                // If it is positive then it might fit an unsigned long long.
        
                qulonglong uval = sipLong_AsUnsignedLongLong(a0);
        
                if (!PyErr_Occurred())
                {
                    sipRes = new QByteArray(QByteArray::number(uval, a1));
                }
                else
                {
                    sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                            ? sipErrorFail : sipErrorContinue);
                }
            }
        }
%End

    static QByteArray fromBase64(const QByteArray &base64);
    static QByteArray fromRawData(const char * /Array/, int size /ArraySize/);
    static QByteArray fromHex(const QByteArray &hexEncoded);
    int count() const /__len__/;
    int length() const;
    bool isNull() const;
    int size() const;
    char at(int i) const /Encoding="None"/;
    char operator[](int i) const /Encoding="None"/;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = sipCpp->operator[]((int)idx);
%End

    QByteArray operator[](SIP_PYSLICE slice) const;
%MethodCode
        Py_ssize_t start, stop, step, slicelength;
        
        if (sipConvertFromSliceObject(a0, sipCpp->length(), &start, &stop, &step, &slicelength) < 0)
        {
            sipIsErr = 1;
        }
        else
        {
            sipRes = new QByteArray();
        
            for (Py_ssize_t i = 0; i < slicelength; ++i)
            {
                sipRes -> append(sipCpp->at(start));
                start += step;
            }
        }
%End

    int __contains__(const QByteArray &a) const;
%MethodCode
        // It looks like you can't assign QBool to int.
        sipRes = bool(sipCpp->contains(*a0));
%End

    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    SIP_PYOBJECT __str__() const /TypeHint="str"/;
%MethodCode
        sipRes = QByteArrayToPyStr(sipCpp);
        
        #if PY_MAJOR_VERSION >= 3
        PyObject *repr = PyObject_Repr(sipRes);
        
        if (repr)
        {
            Py_DECREF(sipRes);
            sipRes = repr;
        }
        #endif
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QByteArray()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QByteArray()");
        #endif
        }
        else
        {
            PyObject *str = QByteArrayToPyStr(sipCpp);
        
            if (str)
            {
        #if PY_MAJOR_VERSION >= 3
                sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QByteArray(%R)", str);
        #else
                sipRes = PyString_FromString("PyQt5.QtCore.QByteArray(");
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(str));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        
                Py_DECREF(str);
            }
        }
%End

    QByteArray operator*(int m) const;
%MethodCode
        sipRes = new QByteArray();
        
        while (a0-- > 0)
            *sipRes += *sipCpp;
%End

    QByteArray &operator*=(int m);
%MethodCode
        QByteArray orig(*sipCpp);
        
        sipCpp->clear();
        
        while (a0-- > 0)
            *sipCpp += orig;
%End

    bool isEmpty() const;
    SIP_PYOBJECT data() /TypeHint="Py_v3:bytes;str"/;
%MethodCode
        // QByteArrays may contain embedded '\0's so set the size explicitly.
        
        char *res = sipCpp->data();
        int len = sipCpp->size();
        
        if (res)
        {
            if ((sipRes = SIPBytes_FromStringAndSize(res, len)) == NULL)
                sipIsErr = 1;
        }
        else
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
%End

    int capacity() const;
    void reserve(int size);
    void squeeze();
    void push_back(const QByteArray &a);
    void push_front(const QByteArray &a);
    bool contains(const QByteArray &a) const;
    QByteArray toHex() const;
    QByteArray toPercentEncoding(const QByteArray &exclude = QByteArray(), const QByteArray &include = QByteArray(), char percent = '%') const;
    static QByteArray fromPercentEncoding(const QByteArray &input, char percent = '%');
    QByteArray repeated(int times) const;
    void swap(QByteArray &other /Constrained/);
%If (Qt_5_2_0 -)

    enum Base64Option
    {
        Base64Encoding,
        Base64UrlEncoding,
        KeepTrailingEquals,
        OmitTrailingEquals,
%If (Qt_5_15_0 -)
        IgnoreBase64DecodingErrors,
%End
%If (Qt_5_15_0 -)
        AbortOnBase64DecodingErrors,
%End
    };

%End
%If (Qt_5_2_0 -)
    typedef QFlags<QByteArray::Base64Option> Base64Options;
%End
%If (Qt_5_2_0 -)
    QByteArray toBase64(QByteArray::Base64Options options) const;
%End
%If (Qt_5_2_0 -)
    static QByteArray fromBase64(const QByteArray &base64, QByteArray::Base64Options options);
%End
%If (Qt_5_7_0 -)
    QByteArray &prepend(int count, char c /Encoding="None"/);
%End
%If (Qt_5_7_0 -)
    QByteArray &append(int count, char c /Encoding="None"/);
%End
%If (Qt_5_7_0 -)
    QByteArray &insert(int i, int count, char c /Encoding="None"/);
%End
%If (Qt_5_9_0 -)
    QByteArray toHex(char separator) const;
%End
%If (Qt_5_10_0 -)
    QByteArray chopped(int len) const;
%End
%If (Qt_5_12_0 -)
    int compare(const QByteArray &a, Qt::CaseSensitivity cs = Qt::CaseSensitive) const;
%End
%If (Qt_5_12_0 -)
    bool isUpper() const;
%End
%If (Qt_5_12_0 -)
    bool isLower() const;
%End
%If (Qt_5_15_0 -)

    enum class Base64DecodingStatus
    {
        Ok,
        IllegalInputLength,
        IllegalCharacter,
        IllegalPadding,
    };

%End
%If (Qt_5_15_0 -)
    static QByteArray::FromBase64Result fromBase64Encoding(const QByteArray &base64, QByteArray::Base64Options options = QByteArray::Base64Encoding);
%End
%If (Qt_5_15_0 -)

    class FromBase64Result
    {
%TypeHeaderCode
#include <qbytearray.h>
%End

    public:
        QByteArray decoded;
        QByteArray::Base64DecodingStatus decodingStatus;
        void swap(QByteArray::FromBase64Result &other /Constrained/);
        operator bool() const;
%MethodCode
            // This is required because SIP doesn't handle operator bool() properly.
            sipRes = sipCpp->operator bool();
%End

        long __hash__() const;
%MethodCode
            sipRes = qHash(*sipCpp);
%End
    };

%End
};

bool operator==(const QByteArray &a1, const QByteArray &a2);
bool operator!=(const QByteArray &a1, const QByteArray &a2);
bool operator<(const QByteArray &a1, const QByteArray &a2);
bool operator<=(const QByteArray &a1, const QByteArray &a2);
bool operator>(const QByteArray &a1, const QByteArray &a2);
bool operator>=(const QByteArray &a1, const QByteArray &a2);
const QByteArray operator+(const QByteArray &a1, const QByteArray &a2);
QDataStream &operator<<(QDataStream &, const QByteArray & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QByteArray & /Constrained/) /ReleaseGIL/;
QByteArray qCompress(const QByteArray &data, int compressionLevel = -1);
QByteArray qUncompress(const QByteArray &data);
%If (Qt_5_2_0 -)
QFlags<QByteArray::Base64Option> operator|(QByteArray::Base64Option f1, QFlags<QByteArray::Base64Option> f2);
%End
quint16 qChecksum(const char *s /Array/, uint len /ArraySize/);
%If (Qt_5_9_0 -)
quint16 qChecksum(const char *s /Array/, uint len /ArraySize/, Qt::ChecksumType standard);
%End
%If (Qt_5_15_0 -)
bool operator==(const QByteArray::FromBase64Result &lhs, const QByteArray::FromBase64Result &rhs);
%End
%If (Qt_5_15_0 -)
bool operator!=(const QByteArray::FromBase64Result &lhs, const QByteArray::FromBase64Result &rhs);
%End
