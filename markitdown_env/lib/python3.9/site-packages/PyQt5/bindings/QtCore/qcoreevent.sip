// qcoreevent.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QEvent /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QEvent::Timer:
        sipType = sipType_QTimerEvent;
        break;
    
    case QEvent::ChildAdded:
    case QEvent::ChildPolished:
    case QEvent::ChildRemoved:
        sipType = sipType_QChildEvent;
        break;
    
    case QEvent::DynamicPropertyChange:
        sipType = sipType_QDynamicPropertyChangeEvent;
        break;
    
    case QEvent::StateMachineSignal:
        sipType = sipType_QStateMachine_SignalEvent;
        break;
    
    case QEvent::StateMachineWrapped:
        sipType = sipType_QStateMachine_WrappedEvent;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    enum Type
    {
        None /PyName=None_/,
        Timer,
        MouseButtonPress,
        MouseButtonRelease,
        MouseButtonDblClick,
        MouseMove,
        KeyPress,
        KeyRelease,
        FocusIn,
        FocusOut,
        Enter,
        Leave,
        Paint,
        Move,
        Resize,
        Show,
        Hide,
        Close,
        ParentChange,
        ParentAboutToChange,
        ThreadChange,
        WindowActivate,
        WindowDeactivate,
        ShowToParent,
        HideToParent,
        Wheel,
        WindowTitleChange,
        WindowIconChange,
        ApplicationWindowIconChange,
        ApplicationFontChange,
        ApplicationLayoutDirectionChange,
        ApplicationPaletteChange,
        PaletteChange,
        Clipboard,
        MetaCall,
        SockAct,
        WinEventAct,
        DeferredDelete,
        DragEnter,
        DragMove,
        DragLeave,
        Drop,
        ChildAdded,
        ChildPolished,
        ChildRemoved,
        PolishRequest,
        Polish,
        LayoutRequest,
        UpdateRequest,
        UpdateLater,
        ContextMenu,
        InputMethod,
        TabletMove,
        LocaleChange,
        LanguageChange,
        LayoutDirectionChange,
        TabletPress,
        TabletRelease,
        OkRequest,
        IconDrag,
        FontChange,
        EnabledChange,
        ActivationChange,
        StyleChange,
        IconTextChange,
        ModifiedChange,
        MouseTrackingChange,
        WindowBlocked,
        WindowUnblocked,
        WindowStateChange,
        ToolTip,
        WhatsThis,
        StatusTip,
        ActionChanged,
        ActionAdded,
        ActionRemoved,
        FileOpen,
        Shortcut,
        ShortcutOverride,
        WhatsThisClicked,
        ToolBarChange,
        ApplicationActivate,
        ApplicationActivated,
        ApplicationDeactivate,
        ApplicationDeactivated,
        QueryWhatsThis,
        EnterWhatsThisMode,
        LeaveWhatsThisMode,
        ZOrderChange,
        HoverEnter,
        HoverLeave,
        HoverMove,
        GraphicsSceneMouseMove,
        GraphicsSceneMousePress,
        GraphicsSceneMouseRelease,
        GraphicsSceneMouseDoubleClick,
        GraphicsSceneContextMenu,
        GraphicsSceneHoverEnter,
        GraphicsSceneHoverMove,
        GraphicsSceneHoverLeave,
        GraphicsSceneHelp,
        GraphicsSceneDragEnter,
        GraphicsSceneDragMove,
        GraphicsSceneDragLeave,
        GraphicsSceneDrop,
        GraphicsSceneWheel,
        GraphicsSceneResize,
        GraphicsSceneMove,
        KeyboardLayoutChange,
        DynamicPropertyChange,
        TabletEnterProximity,
        TabletLeaveProximity,
        NonClientAreaMouseMove,
        NonClientAreaMouseButtonPress,
        NonClientAreaMouseButtonRelease,
        NonClientAreaMouseButtonDblClick,
        MacSizeChange,
        ContentsRectChange,
        CursorChange,
        ToolTipChange,
        GrabMouse,
        UngrabMouse,
        GrabKeyboard,
        UngrabKeyboard,
        StateMachineSignal,
        StateMachineWrapped,
        TouchBegin,
        TouchUpdate,
        TouchEnd,
%If (Qt_5_2_0 -)
        NativeGesture,
%End
        RequestSoftwareInputPanel,
        CloseSoftwareInputPanel,
        WinIdChange,
        Gesture,
        GestureOverride,
        FocusAboutToChange,
        ScrollPrepare,
        Scroll,
        Expose,
        InputMethodQuery,
        OrientationChange,
        TouchCancel,
        PlatformPanel,
%If (Qt_5_1_0 -)
        ApplicationStateChange,
%End
%If (Qt_5_4_0 -)
        ReadOnlyChange,
%End
%If (Qt_5_5_0 -)
        PlatformSurface,
%End
%If (Qt_5_9_0 -)
        TabletTrackingChange,
%End
        EnterEditFocus,
        LeaveEditFocus,
        User,
        MaxUser,
    };

    explicit QEvent(QEvent::Type type);
    QEvent(const QEvent &other);
    virtual ~QEvent();
    QEvent::Type type() const;
    bool spontaneous() const;
    void setAccepted(bool accepted);
    bool isAccepted() const;
    void accept();
    void ignore();
    static int registerEventType(int hint = -1);
};

class QTimerEvent : public QEvent
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

public:
    explicit QTimerEvent(int timerId);
    virtual ~QTimerEvent();
    int timerId() const;
};

class QChildEvent : public QEvent
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

public:
    QChildEvent(QEvent::Type type, QObject *child);
    virtual ~QChildEvent();
    QObject *child() const;
    bool added() const;
    bool polished() const;
    bool removed() const;
};

class QDynamicPropertyChangeEvent : public QEvent
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

public:
    explicit QDynamicPropertyChangeEvent(const QByteArray &name);
    virtual ~QDynamicPropertyChangeEvent();
    QByteArray propertyName() const;
};
