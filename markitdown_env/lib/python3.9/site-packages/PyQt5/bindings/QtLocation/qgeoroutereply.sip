// qgeoroutereply.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QGeoRouteReply : public QObject
{
%TypeHeaderCode
#include <qgeoroutereply.h>
%End

public:
    enum Error
    {
        NoError,
        EngineNotSetError,
        CommunicationError,
        ParseError,
        UnsupportedOptionError,
        UnknownError,
    };

    QGeoRouteReply(QGeoRouteReply::Error error, const QString &errorString, QObject *parent /TransferThis/ = 0);
    virtual ~QGeoRouteReply();
    bool isFinished() const;
    QGeoRouteReply::Error error() const;
    QString errorString() const;
    QGeoRouteRequest request() const;
    QList<QGeoRoute> routes() const;
    virtual void abort();

signals:
%If (Qt_5_9_0 -)
    void aborted();
%End
    void finished();
    void error(QGeoRouteReply::Error error, const QString &errorString = QString());

protected:
    QGeoRouteReply(const QGeoRouteRequest &request, QObject *parent /TransferThis/ = 0);
    void setError(QGeoRouteReply::Error error, const QString &errorString);
    void setFinished(bool finished);
    void setRoutes(const QList<QGeoRoute> &routes);
    void addRoutes(const QList<QGeoRoute> &routes);
};

%End
