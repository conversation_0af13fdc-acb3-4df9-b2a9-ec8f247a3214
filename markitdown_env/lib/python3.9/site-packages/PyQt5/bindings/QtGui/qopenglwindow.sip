// qopenglwindow.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)
%If (PyQt_OpenGL)

class QOpenGLWindow : public QPaintDeviceWindow
{
%TypeHeaderCode
#include <qopenglwindow.h>
%End

public:
    enum UpdateBehavior
    {
        NoPartialUpdate,
        PartialUpdateBlit,
        PartialUpdateBlend,
    };

    QOpenGLWindow(QOpenGLWindow::UpdateBehavior updateBehavior = QOpenGLWindow::NoPartialUpdate, QWindow *parent /TransferThis/ = 0);
%If (Qt_5_5_0 -)
    QOpenGLWindow(QOpenGLContext *shareContext, QOpenGLWindow::UpdateBehavior updateBehavior = QOpenGLWindow::NoPartialUpdate, QWindow *parent /TransferThis/ = 0);
%End
%If (Qt_5_5_0 -)
    virtual ~QOpenGLWindow();
%End
    QOpenGLWindow::UpdateBehavior updateBehavior() const;
    bool isValid() const;
    void makeCurrent();
    void doneCurrent();
    QOpenGLContext *context() const;
    GLuint defaultFramebufferObject() const;
    QImage grabFramebuffer();
%If (Qt_5_5_0 -)
    QOpenGLContext *shareContext() const;
%End

signals:
    void frameSwapped();

protected:
    virtual void initializeGL();
    virtual void resizeGL(int w, int h);
    virtual void paintGL();
    virtual void paintUnderGL();
    virtual void paintOverGL();
    virtual void paintEvent(QPaintEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;
};

%End
%End
