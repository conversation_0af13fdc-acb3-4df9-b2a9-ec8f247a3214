// qwindow.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWindow : public QObject, public QSurface
{
%TypeHeaderCode
#include <qwindow.h>
%End

public:
    explicit QWindow(QScreen *screen = 0);
    explicit QWindow(QWindow *parent /TransferThis/);
    virtual ~QWindow();
    void setSurfaceType(QSurface::SurfaceType surfaceType);
    virtual QSurface::SurfaceType surfaceType() const;
    bool isVisible() const;
    void create();
    WId winId() const;
    QWindow *parent() const;
    void setParent(QWindow *parent /Transfer/);
    bool isTopLevel() const;
    bool isModal() const;
    Qt::WindowModality modality() const;
    void setModality(Qt::WindowModality modality);
    void setFormat(const QSurfaceFormat &format);
    virtual QSurfaceFormat format() const;
    QSurfaceFormat requestedFormat() const;
    void setFlags(Qt::WindowFlags flags);
    Qt::WindowFlags flags() const;
    Qt::WindowType type() const;
    QString title() const;
    void setOpacity(qreal level);

public slots:
    void requestActivate();

public:
    bool isActive() const;
    void reportContentOrientationChange(Qt::ScreenOrientation orientation);
    Qt::ScreenOrientation contentOrientation() const;
    qreal devicePixelRatio() const;
    Qt::WindowState windowState() const;
    void setWindowState(Qt::WindowState state);
    void setTransientParent(QWindow *parent);
    QWindow *transientParent() const;

    enum AncestorMode
    {
        ExcludeTransients,
        IncludeTransients,
    };

    bool isAncestorOf(const QWindow *child, QWindow::AncestorMode mode = QWindow::IncludeTransients) const;
    bool isExposed() const;
    int minimumWidth() const;
    int minimumHeight() const;
    int maximumWidth() const;
    int maximumHeight() const;
    QSize minimumSize() const;
    QSize maximumSize() const;
    QSize baseSize() const;
    QSize sizeIncrement() const;
    void setMinimumSize(const QSize &size);
    void setMaximumSize(const QSize &size);
    void setBaseSize(const QSize &size);
    void setSizeIncrement(const QSize &size);
    void setGeometry(int posx, int posy, int w, int h);
    void setGeometry(const QRect &rect);
    QRect geometry() const;
    QMargins frameMargins() const;
    QRect frameGeometry() const;
    QPoint framePosition() const;
    void setFramePosition(const QPoint &point);
    int width() const;
    int height() const;
    int x() const;
    int y() const;
    virtual QSize size() const;
    QPoint position() const;
    void setPosition(const QPoint &pt);
    void setPosition(int posx, int posy);
    void resize(const QSize &newSize);
    void resize(int w, int h);
    void setFilePath(const QString &filePath);
    QString filePath() const;
    void setIcon(const QIcon &icon);
    QIcon icon() const;
    void destroy();
    bool setKeyboardGrabEnabled(bool grab);
    bool setMouseGrabEnabled(bool grab);
    QScreen *screen() const;
    void setScreen(QScreen *screen);
    virtual QObject *focusObject() const;
    QPoint mapToGlobal(const QPoint &pos) const;
    QPoint mapFromGlobal(const QPoint &pos) const;
    QCursor cursor() const;
    void setCursor(const QCursor &);
    void unsetCursor();

public slots:
    void setVisible(bool visible);
    void show() /ReleaseGIL/;
    void hide();
    void showMinimized() /ReleaseGIL/;
    void showMaximized() /ReleaseGIL/;
    void showFullScreen() /ReleaseGIL/;
    void showNormal() /ReleaseGIL/;
    bool close();
    void raise() /PyName=raise_/;
    void lower();
    void setTitle(const QString &);
    void setX(int arg);
    void setY(int arg);
    void setWidth(int arg);
    void setHeight(int arg);
    void setMinimumWidth(int w);
    void setMinimumHeight(int h);
    void setMaximumWidth(int w);
    void setMaximumHeight(int h);
%If (Qt_5_1_0 -)
    void alert(int msec);
%End
%If (Qt_5_5_0 -)
    void requestUpdate();
%End

signals:
    void screenChanged(QScreen *screen);
    void modalityChanged(Qt::WindowModality modality);
    void windowStateChanged(Qt::WindowState windowState);
    void xChanged(int arg);
    void yChanged(int arg);
    void widthChanged(int arg);
    void heightChanged(int arg);
    void minimumWidthChanged(int arg);
    void minimumHeightChanged(int arg);
    void maximumWidthChanged(int arg);
    void maximumHeightChanged(int arg);
    void visibleChanged(bool arg);
    void contentOrientationChanged(Qt::ScreenOrientation orientation);
    void focusObjectChanged(QObject *object);
%If (Qt_5_3_0 -)
    void windowTitleChanged(const QString &title);
%End

protected:
    virtual void exposeEvent(QExposeEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void moveEvent(QMoveEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual bool event(QEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual void touchEvent(QTouchEvent *);
    virtual void tabletEvent(QTabletEvent *);

public:
%If (Qt_5_1_0 -)

    enum Visibility
    {
        Hidden,
        AutomaticVisibility,
        Windowed,
        Minimized,
        Maximized,
        FullScreen,
    };

%End
%If (Qt_5_1_0 -)
    QWindow::Visibility visibility() const;
%End
%If (Qt_5_1_0 -)
    void setVisibility(QWindow::Visibility v);
%End
%If (Qt_5_1_0 -)
    qreal opacity() const;
%End
%If (Qt_5_1_0 -)
    void setMask(const QRegion &region);
%End
%If (Qt_5_1_0 -)
    QRegion mask() const;
%End
%If (Qt_5_1_0 -)
    static QWindow *fromWinId(WId id);
%End

signals:
%If (Qt_5_1_0 -)
    void visibilityChanged(QWindow::Visibility visibility);
%End
%If (Qt_5_1_0 -)
    void activeChanged();
%End
%If (Qt_5_1_0 -)
    void opacityChanged(qreal opacity);
%End

public:
%If (Qt_5_9_0 -)
    QWindow *parent(QWindow::AncestorMode mode) const;
%End
%If (Qt_5_9_0 -)
    void setFlag(Qt::WindowType, bool on = true);
%End
%If (Qt_5_10_0 -)
    Qt::WindowStates windowStates() const;
%End
%If (Qt_5_10_0 -)
    void setWindowStates(Qt::WindowStates states);
%End

public slots:
%If (Qt_5_15_0 -)
    bool startSystemResize(Qt::Edges edges);
%End
%If (Qt_5_15_0 -)
    bool startSystemMove();
%End
};
