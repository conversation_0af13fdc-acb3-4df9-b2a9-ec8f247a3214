// qpaintengine.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextItem
{
%TypeHeaderCode
#include <qpaintengine.h>
%End

public:
    enum RenderFlag
    {
        RightToLeft,
        Overline,
        Underline,
        StrikeOut,
    };

    typedef QFlags<QTextItem::RenderFlag> RenderFlags;
    qreal descent() const;
    qreal ascent() const;
    qreal width() const;
    QTextItem::RenderFlags renderFlags() const;
    QString text() const;
    QFont font() const;
};

QFlags<QTextItem::RenderFlag> operator|(QTextItem::RenderFlag f1, QFlags<QTextItem::RenderFlag> f2);

class QPaintEngine
{
%TypeHeaderCode
#include <qpaintengine.h>
%End

public:
    enum PaintEngineFeature
    {
        PrimitiveTransform,
        PatternTransform,
        PixmapTransform,
        PatternBrush,
        LinearGradientFill,
        RadialGradientFill,
        ConicalGradientFill,
        AlphaBlend,
        PorterDuff,
        PainterPaths,
        Antialiasing,
        BrushStroke,
        ConstantOpacity,
        MaskedBrush,
        PaintOutsidePaintEvent,
        PerspectiveTransform,
        BlendModes,
        ObjectBoundingModeGradients,
        RasterOpModes,
        AllFeatures,
    };

    typedef QFlags<QPaintEngine::PaintEngineFeature> PaintEngineFeatures;

    enum DirtyFlag
    {
        DirtyPen,
        DirtyBrush,
        DirtyBrushOrigin,
        DirtyFont,
        DirtyBackground,
        DirtyBackgroundMode,
        DirtyTransform,
        DirtyClipRegion,
        DirtyClipPath,
        DirtyHints,
        DirtyCompositionMode,
        DirtyClipEnabled,
        DirtyOpacity,
        AllDirty,
    };

    typedef QFlags<QPaintEngine::DirtyFlag> DirtyFlags;

    enum PolygonDrawMode
    {
        OddEvenMode,
        WindingMode,
        ConvexMode,
        PolylineMode,
    };

    explicit QPaintEngine(QPaintEngine::PaintEngineFeatures features = QPaintEngine::PaintEngineFeatures());
    virtual ~QPaintEngine();
    bool isActive() const;
    void setActive(bool newState);
    virtual bool begin(QPaintDevice *pdev) = 0;
    virtual bool end() = 0;
    virtual void updateState(const QPaintEngineState &state /NoCopy/) = 0;
    virtual void drawRects(const QRect *rects /Array/, int rectCount /ArraySize/);
    virtual void drawRects(const QRectF *rects /Array/, int rectCount /ArraySize/);
    virtual void drawLines(const QLine *lines /Array/, int lineCount /ArraySize/);
    virtual void drawLines(const QLineF *lines /Array/, int lineCount /ArraySize/);
    virtual void drawEllipse(const QRectF &r);
    virtual void drawEllipse(const QRect &r);
    virtual void drawPath(const QPainterPath &path);
    virtual void drawPoints(const QPointF *points /Array/, int pointCount /ArraySize/);
    virtual void drawPoints(const QPoint *points /Array/, int pointCount /ArraySize/);
    virtual void drawPolygon(const QPointF *points /Array/, int pointCount /ArraySize/, QPaintEngine::PolygonDrawMode mode);
    virtual void drawPolygon(const QPoint *points /Array/, int pointCount /ArraySize/, QPaintEngine::PolygonDrawMode mode);
    virtual void drawPixmap(const QRectF &r, const QPixmap &pm, const QRectF &sr) = 0;
    virtual void drawTextItem(const QPointF &p, const QTextItem &textItem /NoCopy/);
    virtual void drawTiledPixmap(const QRectF &r, const QPixmap &pixmap, const QPointF &s);
    virtual void drawImage(const QRectF &r, const QImage &pm, const QRectF &sr, Qt::ImageConversionFlags flags = Qt::AutoColor);
    void setPaintDevice(QPaintDevice *device);
    QPaintDevice *paintDevice() const;

    enum Type
    {
        X11,
        Windows,
        QuickDraw,
        CoreGraphics,
        MacPrinter,
        QWindowSystem,
        PostScript,
        OpenGL,
        Picture,
        SVG,
        Raster,
        Direct3D,
        Pdf,
        OpenVG,
        OpenGL2,
        PaintBuffer,
        Blitter,
%If (Qt_5_3_0 -)
        Direct2D,
%End
        User,
        MaxUser,
    };

    virtual QPaintEngine::Type type() const = 0;
    QPainter *painter() const;
    bool hasFeature(QPaintEngine::PaintEngineFeatures feature) const;

private:
    QPaintEngine(const QPaintEngine &);
};

QFlags<QPaintEngine::PaintEngineFeature> operator|(QPaintEngine::PaintEngineFeature f1, QFlags<QPaintEngine::PaintEngineFeature> f2);

class QPaintEngineState
{
%TypeHeaderCode
#include <qpaintengine.h>
%End

public:
    QPaintEngine::DirtyFlags state() const;
    QPen pen() const;
    QBrush brush() const;
    QPointF brushOrigin() const;
    QBrush backgroundBrush() const;
    Qt::BGMode backgroundMode() const;
    QFont font() const;
    qreal opacity() const;
    Qt::ClipOperation clipOperation() const;
    QRegion clipRegion() const;
    QPainterPath clipPath() const;
    bool isClipEnabled() const;
    QPainter::RenderHints renderHints() const;
    QPainter::CompositionMode compositionMode() const;
    QPainter *painter() const;
    QTransform transform() const;
    bool brushNeedsResolving() const;
    bool penNeedsResolving() const;
};
