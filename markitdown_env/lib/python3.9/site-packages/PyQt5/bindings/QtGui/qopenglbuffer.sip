// qopenglbuffer.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_OpenGL)

class QOpenGLBuffer
{
%TypeHeaderCode
#include <qopenglbuffer.h>
%End

public:
    enum Type
    {
        VertexB<PERSON>er,
        <PERSON><PERSON>uffer,
        <PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON>xelUnpackBuffer,
    };

    QOpenGLBuffer();
    explicit QOpenGLBuffer(QOpenGLBuffer::Type type);
    QOpenGLBuffer(const QOpenGLBuffer &other);
    ~QOpenGLBuffer();

    enum UsagePattern
    {
        StreamDraw,
        StreamRead,
        StreamCopy,
        StaticDraw,
        StaticRead,
        StaticCopy,
        DynamicDraw,
        DynamicRead,
        DynamicCopy,
    };

    enum Access
    {
        ReadOnly,
        WriteOnly,
        ReadWrite,
    };

    QOpenGLBuffer::Type type() const;
    QOpenGLBuffer::UsagePattern usagePattern() const;
    void setUsagePattern(QOpenGLBuffer::UsagePattern value);
    bool create();
    bool isCreated() const;
    void destroy();
    bool bind();
    void release();
    static void release(QOpenGLBuffer::Type type);
    GLuint bufferId() const;
    int size() const /__len__/;
    bool read(int offset, void *data, int count);
    void write(int offset, const void *data, int count);
    void allocate(const void *data, int count);
    void allocate(int count);
    void *map(QOpenGLBuffer::Access access);
    bool unmap();
%If (Qt_5_4_0 -)

    enum RangeAccessFlag
    {
        RangeRead,
        RangeWrite,
        RangeInvalidate,
        RangeInvalidateBuffer,
        RangeFlushExplicit,
        RangeUnsynchronized,
    };

%End
%If (Qt_5_4_0 -)
    typedef QFlags<QOpenGLBuffer::RangeAccessFlag> RangeAccessFlags;
%End
%If (Qt_5_4_0 -)
    void *mapRange(int offset, int count, QOpenGLBuffer::RangeAccessFlags access);
%End
};

%End
%If (Qt_5_4_0 -)
%If (PyQt_OpenGL)
QFlags<QOpenGLBuffer::RangeAccessFlag> operator|(QOpenGLBuffer::RangeAccessFlag f1, QFlags<QOpenGLBuffer::RangeAccessFlag> f2);
%End
%End
