// qtremoteobjectglobal.sip generated by MetaSIP
//
// This file is part of the QtRemoteObjects Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_12_0 -)

struct QRemoteObjectSourceLocationInfo
{
%TypeHeaderCode
#include <qtremoteobjectglobal.h>
%End

    QRemoteObjectSourceLocationInfo();
    QRemoteObjectSourceLocationInfo(const QString &typeName_, const QUrl &hostUrl_);
    bool operator==(const QRemoteObjectSourceLocationInfo &other) const;
    bool operator!=(const QRemoteObjectSourceLocationInfo &other) const;
    QString typeName;
    QUrl hostUrl;
};

%End
%If (Qt_5_12_0 -)
QDataStream &operator<<(QDataStream &stream, const QRemoteObjectSourceLocationInfo &info /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_12_0 -)
QDataStream &operator>>(QDataStream &stream, QRemoteObjectSourceLocationInfo &info /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_12_0 -)
typedef QPair<QString, QRemoteObjectSourceLocationInfo> QRemoteObjectSourceLocation;
%End
%If (Qt_5_12_0 -)
typedef QHash<QString, QRemoteObjectSourceLocationInfo> QRemoteObjectSourceLocations;
%End
%If (Qt_5_12_0 -)

namespace QtRemoteObjects
{
%TypeHeaderCode
#include <qtremoteobjectglobal.h>
%End

    enum InitialAction
    {
        FetchRootSize,
        PrefetchData,
    };
};

%End
