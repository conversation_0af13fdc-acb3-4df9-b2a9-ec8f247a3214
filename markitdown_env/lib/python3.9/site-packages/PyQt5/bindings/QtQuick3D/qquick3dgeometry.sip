// qquick3dgeometry.sip generated by MetaSIP
//
// This file is part of the QtQuick3D Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_15_0 -)

class QQuick3DGeometry : public QQuick3DObject
{
%TypeHeaderCode
#include <qquick3dgeometry.h>
%End

public:
    explicit QQuick3DGeometry(QQuick3DObject *parent /TransferThis/ = 0);
    virtual ~QQuick3DGeometry();

    enum class PrimitiveType
    {
        Unknown,
        Points,
        LineStrip,
        Lines,
        TriangleStrip,
        TriangleFan,
        Triangles,
    };

    struct Attribute
    {
%TypeHeaderCode
#include <qquick3dgeometry.h>
%End

        enum Semantic
        {
            UnknownSemantic,
            IndexSemantic,
            PositionSemantic,
            NormalSemantic,
            TexCoordSemantic,
            TangentSemantic,
            BinormalSemantic,
        };

        enum ComponentType
        {
            DefaultType,
            U16Type,
            U32Type,
            F32Type,
        };

        QQuick3DGeometry::Attribute::Semantic semantic;
        int offset;
        QQuick3DGeometry::Attribute::ComponentType componentType;
    };

    QString name() const;
    QByteArray vertexBuffer() const;
    QByteArray indexBuffer() const;
    int attributeCount() const;
    QQuick3DGeometry::Attribute attribute(int index) const;
    QQuick3DGeometry::PrimitiveType primitiveType() const;
    QVector3D boundsMin() const;
    QVector3D boundsMax() const;
    int stride() const;
    void setVertexData(const QByteArray &data);
    void setIndexData(const QByteArray &data);
    void setStride(int stride);
    void setBounds(const QVector3D &min, const QVector3D &max);
    void setPrimitiveType(QQuick3DGeometry::PrimitiveType type);
    void addAttribute(QQuick3DGeometry::Attribute::Semantic semantic, int offset, QQuick3DGeometry::Attribute::ComponentType componentType);
    void addAttribute(const QQuick3DGeometry::Attribute &att);
    void clear();

public slots:
    void setName(const QString &name);

signals:
    void nameChanged();
};

%End
