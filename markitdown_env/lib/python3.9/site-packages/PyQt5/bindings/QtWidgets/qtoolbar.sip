// qtoolbar.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QToolBar : public QWidget
{
%TypeHeaderCode
#include <qtoolbar.h>
%End

public:
    QToolBar(const QString &title, QWidget *parent /TransferThis/ = 0);
    explicit QToolBar(QWidget *parent /TransferThis/ = 0);
    virtual ~QToolBar();
    void setMovable(bool movable);
    bool isMovable() const;
    void setAllowedAreas(Qt::ToolBarAreas areas);
    Qt::ToolBarAreas allowedAreas() const;
    bool isAreaAllowed(Qt::ToolBarArea area) const;
    void setOrientation(Qt::Orientation orientation);
    Qt::Orientation orientation() const;
    void clear();
    void addAction(QAction *action);
    QAction *addAction(const QString &text) /Transfer/;
    QAction *addAction(const QIcon &icon, const QString &text) /Transfer/;
    QAction *addAction(const QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt5_qtwidgets_get_connection_parts(a1, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

    QAction *addAction(const QIcon &icon, const QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt5_qtwidgets_get_connection_parts(a2, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, *a1, receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

    QAction *addSeparator() /Transfer/;
    QAction *insertSeparator(QAction *before) /Transfer/;
    QAction *addWidget(QWidget *widget /Transfer/) /Transfer/;
    QAction *insertWidget(QAction *before, QWidget *widget /Transfer/) /Transfer/;
    QRect actionGeometry(QAction *action) const;
    QAction *actionAt(const QPoint &p) const;
    QAction *actionAt(int ax, int ay) const;
    QAction *toggleViewAction() const;
    QSize iconSize() const;
    Qt::ToolButtonStyle toolButtonStyle() const;
    QWidget *widgetForAction(QAction *action) const;

public slots:
    void setIconSize(const QSize &iconSize);
    void setToolButtonStyle(Qt::ToolButtonStyle toolButtonStyle);

signals:
    void actionTriggered(QAction *action);
    void movableChanged(bool movable);
    void allowedAreasChanged(Qt::ToolBarAreas allowedAreas);
    void orientationChanged(Qt::Orientation orientation);
    void iconSizeChanged(const QSize &iconSize);
    void toolButtonStyleChanged(Qt::ToolButtonStyle toolButtonStyle);
    void topLevelChanged(bool topLevel);
    void visibilityChanged(bool visible);

protected:
    void initStyleOption(QStyleOptionToolBar *option) const;
    virtual void actionEvent(QActionEvent *event);
    virtual void changeEvent(QEvent *event);
    virtual void paintEvent(QPaintEvent *event);
    virtual bool event(QEvent *event);

public:
    bool isFloatable() const;
    void setFloatable(bool floatable);
    bool isFloating() const;
};
