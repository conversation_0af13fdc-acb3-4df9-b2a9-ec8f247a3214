# The PEP 484 type hints stub file for the QtQuick module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt5.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import typing

import PyQt5.sip

from PyQt5 import QtCore
from PyQt5 import QtGui
from PyQt5 import QtNetwork
from PyQt5 import QtQml

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_OPENGL_ARRAY = typing.Union[typing.Sequence[int], typing.Sequence[float],
        PyQt5.sip.Buffer, None]
PYQT_OPENGL_BOUND_ARRAY = typing.Union[typing.Sequence[int],
        typing.Sequence[float], PyQt5.sip.Buffer, int, None]


class QQuickItem(QtCore.QObject, QtQml.QQmlParserStatus):

    class TransformOrigin(int):
        TopLeft = ... # type: QQuickItem.TransformOrigin
        Top = ... # type: QQuickItem.TransformOrigin
        TopRight = ... # type: QQuickItem.TransformOrigin
        Left = ... # type: QQuickItem.TransformOrigin
        Center = ... # type: QQuickItem.TransformOrigin
        Right = ... # type: QQuickItem.TransformOrigin
        BottomLeft = ... # type: QQuickItem.TransformOrigin
        Bottom = ... # type: QQuickItem.TransformOrigin
        BottomRight = ... # type: QQuickItem.TransformOrigin

    class ItemChange(int):
        ItemChildAddedChange = ... # type: QQuickItem.ItemChange
        ItemChildRemovedChange = ... # type: QQuickItem.ItemChange
        ItemSceneChange = ... # type: QQuickItem.ItemChange
        ItemVisibleHasChanged = ... # type: QQuickItem.ItemChange
        ItemParentHasChanged = ... # type: QQuickItem.ItemChange
        ItemOpacityHasChanged = ... # type: QQuickItem.ItemChange
        ItemActiveFocusHasChanged = ... # type: QQuickItem.ItemChange
        ItemRotationHasChanged = ... # type: QQuickItem.ItemChange
        ItemAntialiasingHasChanged = ... # type: QQuickItem.ItemChange
        ItemDevicePixelRatioHasChanged = ... # type: QQuickItem.ItemChange
        ItemEnabledHasChanged = ... # type: QQuickItem.ItemChange

    class Flag(int):
        ItemClipsChildrenToShape = ... # type: QQuickItem.Flag
        ItemAcceptsInputMethod = ... # type: QQuickItem.Flag
        ItemIsFocusScope = ... # type: QQuickItem.Flag
        ItemHasContents = ... # type: QQuickItem.Flag
        ItemAcceptsDrops = ... # type: QQuickItem.Flag

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> 'QQuickItem.Flags': ...
        def __xor__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> 'QQuickItem.Flags': ...
        def __ior__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> 'QQuickItem.Flags': ...
        def __or__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> 'QQuickItem.Flags': ...
        def __iand__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> 'QQuickItem.Flags': ...
        def __and__(self, f: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> 'QQuickItem.Flags': ...
        def __invert__(self) -> 'QQuickItem.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ItemChangeData(PyQt5.sipsimplewrapper):

        boolValue = ... # type: bool
        item = ... # type: 'QQuickItem'
        realValue = ... # type: float
        window = ... # type: 'QQuickWindow'

        @typing.overload
        def __init__(self, v: typing.Optional['QQuickItem']) -> None: ...
        @typing.overload
        def __init__(self, v: typing.Optional['QQuickWindow']) -> None: ...
        @typing.overload
        def __init__(self, v: float) -> None: ...
        @typing.overload
        def __init__(self, v: bool) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QQuickItem.ItemChangeData') -> None: ...

    class UpdatePaintNodeData(PyQt5.sipsimplewrapper):

        transformNode = ... # type: 'QSGTransformNode'

        def __init__(self, a0: 'QQuickItem.UpdatePaintNodeData') -> None: ...

    def __init__(self, parent: typing.Optional['QQuickItem'] = ...) -> None: ...

    containmentMaskChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setContainmentMask(self, mask: typing.Optional[QtCore.QObject]) -> None: ...
    def containmentMask(self) -> typing.Optional[QtCore.QObject]: ...
    def setAcceptTouchEvents(self, accept: bool) -> None: ...
    def acceptTouchEvents(self) -> bool: ...
    def size(self) -> QtCore.QSizeF: ...
    def mapFromGlobal(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def mapToGlobal(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def isAncestorOf(self, child: typing.Optional['QQuickItem']) -> bool: ...
    def grabToImage(self, targetSize: QtCore.QSize = ...) -> typing.Optional['QQuickItemGrabResult']: ...
    def resetAntialiasing(self) -> None: ...
    windowChanged: typing.ClassVar[QtCore.pyqtSignal]
    def nextItemInFocusChain(self, forward: bool = ...) -> typing.Optional['QQuickItem']: ...
    def setActiveFocusOnTab(self, a0: bool) -> None: ...
    def activeFocusOnTab(self) -> bool: ...
    def updatePolish(self) -> None: ...
    def releaseResources(self) -> None: ...
    def updatePaintNode(self, a0: typing.Optional['QSGNode'], a1: typing.Optional['QQuickItem.UpdatePaintNodeData']) -> typing.Optional['QSGNode']: ...
    def geometryChanged(self, newGeometry: QtCore.QRectF, oldGeometry: QtCore.QRectF) -> None: ...
    def childMouseEventFilter(self, a0: typing.Optional['QQuickItem'], a1: typing.Optional[QtCore.QEvent]) -> bool: ...
    def dropEvent(self, a0: typing.Optional[QtGui.QDropEvent]) -> None: ...
    def dragLeaveEvent(self, a0: typing.Optional[QtGui.QDragLeaveEvent]) -> None: ...
    def dragMoveEvent(self, a0: typing.Optional[QtGui.QDragMoveEvent]) -> None: ...
    def dragEnterEvent(self, a0: typing.Optional[QtGui.QDragEnterEvent]) -> None: ...
    def hoverLeaveEvent(self, event: typing.Optional[QtGui.QHoverEvent]) -> None: ...
    def hoverMoveEvent(self, event: typing.Optional[QtGui.QHoverEvent]) -> None: ...
    def hoverEnterEvent(self, event: typing.Optional[QtGui.QHoverEvent]) -> None: ...
    def touchEvent(self, event: typing.Optional[QtGui.QTouchEvent]) -> None: ...
    def wheelEvent(self, event: typing.Optional[QtGui.QWheelEvent]) -> None: ...
    def touchUngrabEvent(self) -> None: ...
    def mouseUngrabEvent(self) -> None: ...
    def mouseDoubleClickEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseMoveEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def focusOutEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def focusInEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def inputMethodEvent(self, a0: typing.Optional[QtGui.QInputMethodEvent]) -> None: ...
    def keyReleaseEvent(self, event: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def keyPressEvent(self, event: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def componentComplete(self) -> None: ...
    def classBegin(self) -> None: ...
    def heightValid(self) -> bool: ...
    def widthValid(self) -> bool: ...
    def updateInputMethod(self, queries: typing.Union[QtCore.Qt.InputMethodQueries, QtCore.Qt.InputMethodQuery] = ...) -> None: ...
    def itemChange(self, a0: 'QQuickItem.ItemChange', a1: 'QQuickItem.ItemChangeData') -> None: ...
    def isComponentComplete(self) -> bool: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def update(self) -> None: ...
    def textureProvider(self) -> typing.Optional['QSGTextureProvider']: ...
    def isTextureProvider(self) -> bool: ...
    def inputMethodQuery(self, query: QtCore.Qt.InputMethodQuery) -> typing.Any: ...
    def childAt(self, x: float, y: float) -> typing.Optional['QQuickItem']: ...
    @typing.overload
    def forceActiveFocus(self) -> None: ...
    @typing.overload
    def forceActiveFocus(self, reason: QtCore.Qt.FocusReason) -> None: ...
    def polish(self) -> None: ...
    def mapRectFromScene(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapRectFromItem(self, item: typing.Optional['QQuickItem'], rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapFromScene(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def mapFromItem(self, item: typing.Optional['QQuickItem'], point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def mapRectToScene(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapRectToItem(self, item: typing.Optional['QQuickItem'], rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapToScene(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def mapToItem(self, item: typing.Optional['QQuickItem'], point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> QtCore.QPointF: ...
    def contains(self, point: typing.Union[QtCore.QPointF, QtCore.QPoint]) -> bool: ...
    def setKeepTouchGrab(self, a0: bool) -> None: ...
    def keepTouchGrab(self) -> bool: ...
    def ungrabTouchPoints(self) -> None: ...
    def grabTouchPoints(self, ids: typing.Iterable[int]) -> None: ...
    def setFiltersChildMouseEvents(self, filter: bool) -> None: ...
    def filtersChildMouseEvents(self) -> bool: ...
    def setKeepMouseGrab(self, a0: bool) -> None: ...
    def keepMouseGrab(self) -> bool: ...
    def ungrabMouse(self) -> None: ...
    def grabMouse(self) -> None: ...
    def unsetCursor(self) -> None: ...
    def setCursor(self, cursor: typing.Union[QtGui.QCursor, QtCore.Qt.CursorShape]) -> None: ...
    def cursor(self) -> QtGui.QCursor: ...
    def setAcceptHoverEvents(self, enabled: bool) -> None: ...
    def acceptHoverEvents(self) -> bool: ...
    def setAcceptedMouseButtons(self, buttons: typing.Union[QtCore.Qt.MouseButtons, QtCore.Qt.MouseButton]) -> None: ...
    def acceptedMouseButtons(self) -> QtCore.Qt.MouseButtons: ...
    def scopedFocusItem(self) -> typing.Optional['QQuickItem']: ...
    def isFocusScope(self) -> bool: ...
    @typing.overload
    def setFocus(self, a0: bool) -> None: ...
    @typing.overload
    def setFocus(self, focus: bool, reason: QtCore.Qt.FocusReason) -> None: ...
    def hasFocus(self) -> bool: ...
    def hasActiveFocus(self) -> bool: ...
    def setFlags(self, flags: typing.Union['QQuickItem.Flags', 'QQuickItem.Flag']) -> None: ...
    def setFlag(self, flag: 'QQuickItem.Flag', enabled: bool = ...) -> None: ...
    def flags(self) -> 'QQuickItem.Flags': ...
    def setAntialiasing(self, a0: bool) -> None: ...
    def antialiasing(self) -> bool: ...
    def setSmooth(self, a0: bool) -> None: ...
    def smooth(self) -> bool: ...
    def setEnabled(self, a0: bool) -> None: ...
    def isEnabled(self) -> bool: ...
    def setVisible(self, a0: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def setOpacity(self, a0: float) -> None: ...
    def opacity(self) -> float: ...
    def setScale(self, a0: float) -> None: ...
    def scale(self) -> float: ...
    def setRotation(self, a0: float) -> None: ...
    def rotation(self) -> float: ...
    def setZ(self, a0: float) -> None: ...
    def z(self) -> float: ...
    def setTransformOrigin(self, a0: 'QQuickItem.TransformOrigin') -> None: ...
    def transformOrigin(self) -> 'QQuickItem.TransformOrigin': ...
    def implicitHeight(self) -> float: ...
    def setImplicitHeight(self, a0: float) -> None: ...
    def resetHeight(self) -> None: ...
    def setHeight(self, a0: float) -> None: ...
    def height(self) -> float: ...
    def implicitWidth(self) -> float: ...
    def setImplicitWidth(self, a0: float) -> None: ...
    def resetWidth(self) -> None: ...
    def setWidth(self, a0: float) -> None: ...
    def width(self) -> float: ...
    def setY(self, a0: float) -> None: ...
    def setX(self, a0: float) -> None: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def setBaselineOffset(self, a0: float) -> None: ...
    def baselineOffset(self) -> float: ...
    def setState(self, a0: typing.Optional[str]) -> None: ...
    def state(self) -> str: ...
    def setClip(self, a0: bool) -> None: ...
    def clip(self) -> bool: ...
    def childItems(self) -> typing.List['QQuickItem']: ...
    def childrenRect(self) -> QtCore.QRectF: ...
    def stackAfter(self, a0: typing.Optional['QQuickItem']) -> None: ...
    def stackBefore(self, a0: typing.Optional['QQuickItem']) -> None: ...
    def setParentItem(self, parent: typing.Optional['QQuickItem']) -> None: ...
    def parentItem(self) -> typing.Optional['QQuickItem']: ...
    def window(self) -> typing.Optional['QQuickWindow']: ...


class QQuickFramebufferObject(QQuickItem):

    class Renderer(PyQt5.sip.wrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QQuickFramebufferObject.Renderer') -> None: ...

        def invalidateFramebufferObject(self) -> None: ...
        def update(self) -> None: ...
        def framebufferObject(self) -> typing.Optional[QtGui.QOpenGLFramebufferObject]: ...
        def synchronize(self, a0: typing.Optional['QQuickFramebufferObject']) -> None: ...
        def createFramebufferObject(self, size: QtCore.QSize) -> typing.Optional[QtGui.QOpenGLFramebufferObject]: ...
        def render(self) -> None: ...

    def __init__(self, parent: typing.Optional[QQuickItem] = ...) -> None: ...

    mirrorVerticallyChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setMirrorVertically(self, enable: bool) -> None: ...
    def mirrorVertically(self) -> bool: ...
    def releaseResources(self) -> None: ...
    def textureProvider(self) -> typing.Optional['QSGTextureProvider']: ...
    def isTextureProvider(self) -> bool: ...
    textureFollowsItemSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def updatePaintNode(self, a0: typing.Optional['QSGNode'], a1: typing.Optional[QQuickItem.UpdatePaintNodeData]) -> typing.Optional['QSGNode']: ...
    def geometryChanged(self, newGeometry: QtCore.QRectF, oldGeometry: QtCore.QRectF) -> None: ...
    def createRenderer(self) -> typing.Optional['QQuickFramebufferObject.Renderer']: ...
    def setTextureFollowsItemSize(self, follows: bool) -> None: ...
    def textureFollowsItemSize(self) -> bool: ...


class QQuickTextureFactory(QtCore.QObject):

    def __init__(self) -> None: ...

    @staticmethod
    def textureFactoryForImage(image: QtGui.QImage) -> typing.Optional['QQuickTextureFactory']: ...
    def image(self) -> QtGui.QImage: ...
    def textureByteCount(self) -> int: ...
    def textureSize(self) -> QtCore.QSize: ...
    def createTexture(self, window: typing.Optional['QQuickWindow']) -> typing.Optional['QSGTexture']: ...


class QQuickImageProvider(QtQml.QQmlImageProviderBase):

    @typing.overload
    def __init__(self, type: QtQml.QQmlImageProviderBase.ImageType, flags: typing.Union[QtQml.QQmlImageProviderBase.Flags, QtQml.QQmlImageProviderBase.Flag] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQuickImageProvider') -> None: ...

    def requestTexture(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Tuple[typing.Optional[QQuickTextureFactory], typing.Optional[QtCore.QSize]]: ...
    def requestPixmap(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Tuple[QtGui.QPixmap, typing.Optional[QtCore.QSize]]: ...
    def requestImage(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Tuple[QtGui.QImage, typing.Optional[QtCore.QSize]]: ...
    def flags(self) -> QtQml.QQmlImageProviderBase.Flags: ...
    def imageType(self) -> QtQml.QQmlImageProviderBase.ImageType: ...


class QQuickImageResponse(QtCore.QObject):

    def __init__(self) -> None: ...

    finished: typing.ClassVar[QtCore.pyqtSignal]
    def cancel(self) -> None: ...
    def errorString(self) -> str: ...
    def textureFactory(self) -> typing.Optional[QQuickTextureFactory]: ...


class QQuickAsyncImageProvider(QQuickImageProvider):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQuickAsyncImageProvider') -> None: ...

    def requestImageResponse(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Optional[QQuickImageResponse]: ...


class QQuickItemGrabResult(QtCore.QObject):

    ready: typing.ClassVar[QtCore.pyqtSignal]
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def saveToFile(self, fileName: typing.Optional[str]) -> bool: ...
    def url(self) -> QtCore.QUrl: ...
    def image(self) -> QtGui.QImage: ...


class QQuickPaintedItem(QQuickItem):

    class PerformanceHint(int):
        FastFBOResizing = ... # type: QQuickPaintedItem.PerformanceHint

    class RenderTarget(int):
        Image = ... # type: QQuickPaintedItem.RenderTarget
        FramebufferObject = ... # type: QQuickPaintedItem.RenderTarget
        InvertedYFramebufferObject = ... # type: QQuickPaintedItem.RenderTarget

    class PerformanceHints(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __xor__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __ior__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __or__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __iand__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __and__(self, f: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __invert__(self) -> 'QQuickPaintedItem.PerformanceHints': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QQuickItem] = ...) -> None: ...

    textureSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setTextureSize(self, size: QtCore.QSize) -> None: ...
    def textureSize(self) -> QtCore.QSize: ...
    def itemChange(self, a0: QQuickItem.ItemChange, a1: QQuickItem.ItemChangeData) -> None: ...
    def releaseResources(self) -> None: ...
    def textureProvider(self) -> typing.Optional['QSGTextureProvider']: ...
    def isTextureProvider(self) -> bool: ...
    def updatePaintNode(self, a0: typing.Optional['QSGNode'], a1: typing.Optional[QQuickItem.UpdatePaintNodeData]) -> typing.Optional['QSGNode']: ...
    renderTargetChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsScaleChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    fillColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    def paint(self, painter: typing.Optional[QtGui.QPainter]) -> None: ...
    def setRenderTarget(self, target: 'QQuickPaintedItem.RenderTarget') -> None: ...
    def renderTarget(self) -> 'QQuickPaintedItem.RenderTarget': ...
    def setFillColor(self, a0: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def fillColor(self) -> QtGui.QColor: ...
    def setContentsScale(self, a0: float) -> None: ...
    def contentsScale(self) -> float: ...
    def resetContentsSize(self) -> None: ...
    def setContentsSize(self, a0: QtCore.QSize) -> None: ...
    def contentsSize(self) -> QtCore.QSize: ...
    def contentsBoundingRect(self) -> QtCore.QRectF: ...
    def setPerformanceHints(self, hints: typing.Union['QQuickPaintedItem.PerformanceHints', 'QQuickPaintedItem.PerformanceHint']) -> None: ...
    def setPerformanceHint(self, hint: 'QQuickPaintedItem.PerformanceHint', enabled: bool = ...) -> None: ...
    def performanceHints(self) -> 'QQuickPaintedItem.PerformanceHints': ...
    def setMipmap(self, enable: bool) -> None: ...
    def mipmap(self) -> bool: ...
    def setAntialiasing(self, enable: bool) -> None: ...
    def antialiasing(self) -> bool: ...
    def setOpaquePainting(self, opaque: bool) -> None: ...
    def opaquePainting(self) -> bool: ...
    def update(self, rect: QtCore.QRect = ...) -> None: ...


class QQuickRenderControl(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    sceneChanged: typing.ClassVar[QtCore.pyqtSignal]
    renderRequested: typing.ClassVar[QtCore.pyqtSignal]
    def prepareThread(self, targetThread: typing.Optional[QtCore.QThread]) -> None: ...
    def renderWindow(self, offset: typing.Optional[QtCore.QPoint]) -> typing.Optional[QtGui.QWindow]: ...
    @staticmethod
    def renderWindowFor(win: typing.Optional['QQuickWindow'], offset: typing.Optional[QtCore.QPoint] = ...) -> typing.Optional[QtGui.QWindow]: ...
    def grab(self) -> QtGui.QImage: ...
    def sync(self) -> bool: ...
    def render(self) -> None: ...
    def polishItems(self) -> None: ...
    def invalidate(self) -> None: ...
    def initialize(self, gl: typing.Optional[QtGui.QOpenGLContext]) -> None: ...


class QQuickTextDocument(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QQuickItem]) -> None: ...

    def textDocument(self) -> typing.Optional[QtGui.QTextDocument]: ...


class QQuickWindow(QtGui.QWindow):

    class NativeObjectType(int):
        NativeObjectTexture = ... # type: QQuickWindow.NativeObjectType

    class TextRenderType(int):
        QtTextRendering = ... # type: QQuickWindow.TextRenderType
        NativeTextRendering = ... # type: QQuickWindow.TextRenderType

    class RenderStage(int):
        BeforeSynchronizingStage = ... # type: QQuickWindow.RenderStage
        AfterSynchronizingStage = ... # type: QQuickWindow.RenderStage
        BeforeRenderingStage = ... # type: QQuickWindow.RenderStage
        AfterRenderingStage = ... # type: QQuickWindow.RenderStage
        AfterSwapStage = ... # type: QQuickWindow.RenderStage
        NoStage = ... # type: QQuickWindow.RenderStage

    class SceneGraphError(int):
        ContextNotAvailable = ... # type: QQuickWindow.SceneGraphError

    class CreateTextureOption(int):
        TextureHasAlphaChannel = ... # type: QQuickWindow.CreateTextureOption
        TextureHasMipmaps = ... # type: QQuickWindow.CreateTextureOption
        TextureOwnsGLTexture = ... # type: QQuickWindow.CreateTextureOption
        TextureCanUseAtlas = ... # type: QQuickWindow.CreateTextureOption
        TextureIsOpaque = ... # type: QQuickWindow.CreateTextureOption

    class CreateTextureOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> 'QQuickWindow.CreateTextureOptions': ...
        def __xor__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> 'QQuickWindow.CreateTextureOptions': ...
        def __ior__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> 'QQuickWindow.CreateTextureOptions': ...
        def __or__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> 'QQuickWindow.CreateTextureOptions': ...
        def __iand__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> 'QQuickWindow.CreateTextureOptions': ...
        def __and__(self, f: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> 'QQuickWindow.CreateTextureOptions': ...
        def __invert__(self) -> 'QQuickWindow.CreateTextureOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...

    afterRenderPassRecording: typing.ClassVar[QtCore.pyqtSignal]
    beforeRenderPassRecording: typing.ClassVar[QtCore.pyqtSignal]
    def endExternalCommands(self) -> None: ...
    def beginExternalCommands(self) -> None: ...
    @staticmethod
    def setTextRenderType(renderType: 'QQuickWindow.TextRenderType') -> None: ...
    @staticmethod
    def textRenderType() -> 'QQuickWindow.TextRenderType': ...
    @staticmethod
    def sceneGraphBackend() -> str: ...
    def createImageNode(self) -> typing.Optional['QSGImageNode']: ...
    def createRectangleNode(self) -> typing.Optional['QSGRectangleNode']: ...
    @typing.overload
    @staticmethod
    def setSceneGraphBackend(api: 'QSGRendererInterface.GraphicsApi') -> None: ...
    @typing.overload
    @staticmethod
    def setSceneGraphBackend(backend: typing.Optional[str]) -> None: ...
    def rendererInterface(self) -> typing.Optional['QSGRendererInterface']: ...
    def isSceneGraphInitialized(self) -> bool: ...
    def effectiveDevicePixelRatio(self) -> float: ...
    def scheduleRenderJob(self, job: typing.Optional[QtCore.QRunnable], schedule: 'QQuickWindow.RenderStage') -> None: ...
    sceneGraphError: typing.ClassVar[QtCore.pyqtSignal]
    sceneGraphAboutToStop: typing.ClassVar[QtCore.pyqtSignal]
    afterAnimating: typing.ClassVar[QtCore.pyqtSignal]
    afterSynchronizing: typing.ClassVar[QtCore.pyqtSignal]
    openglContextCreated: typing.ClassVar[QtCore.pyqtSignal]
    def resetOpenGLState(self) -> None: ...
    activeFocusItemChanged: typing.ClassVar[QtCore.pyqtSignal]
    closing: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def setDefaultAlphaBuffer(useAlpha: bool) -> None: ...
    @staticmethod
    def hasDefaultAlphaBuffer() -> bool: ...
    def tabletEvent(self, a0: typing.Optional[QtGui.QTabletEvent]) -> None: ...
    def wheelEvent(self, a0: typing.Optional[QtGui.QWheelEvent]) -> None: ...
    def mouseMoveEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseDoubleClickEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def keyReleaseEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def keyPressEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def focusOutEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def focusInEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def hideEvent(self, a0: typing.Optional[QtGui.QHideEvent]) -> None: ...
    def showEvent(self, a0: typing.Optional[QtGui.QShowEvent]) -> None: ...
    def resizeEvent(self, a0: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    def exposeEvent(self, a0: typing.Optional[QtGui.QExposeEvent]) -> None: ...
    def releaseResources(self) -> None: ...
    def update(self) -> None: ...
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    afterRendering: typing.ClassVar[QtCore.pyqtSignal]
    beforeRendering: typing.ClassVar[QtCore.pyqtSignal]
    beforeSynchronizing: typing.ClassVar[QtCore.pyqtSignal]
    sceneGraphInvalidated: typing.ClassVar[QtCore.pyqtSignal]
    sceneGraphInitialized: typing.ClassVar[QtCore.pyqtSignal]
    frameSwapped: typing.ClassVar[QtCore.pyqtSignal]
    def openglContext(self) -> typing.Optional[QtGui.QOpenGLContext]: ...
    def isPersistentSceneGraph(self) -> bool: ...
    def setPersistentSceneGraph(self, persistent: bool) -> None: ...
    def isPersistentOpenGLContext(self) -> bool: ...
    def setPersistentOpenGLContext(self, persistent: bool) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def clearBeforeRendering(self) -> bool: ...
    def setClearBeforeRendering(self, enabled: bool) -> None: ...
    def createTextureFromNativeObject(self, type: 'QQuickWindow.NativeObjectType', nativeObjectPtr: typing.Optional[PyQt5.sip.voidptr], nativeLayout: int, size: QtCore.QSize, options: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption'] = ...) -> typing.Optional['QSGTexture']: ...
    def createTextureFromId(self, id: int, size: QtCore.QSize, options: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption'] = ...) -> typing.Optional['QSGTexture']: ...
    @typing.overload
    def createTextureFromImage(self, image: QtGui.QImage) -> typing.Optional['QSGTexture']: ...
    @typing.overload
    def createTextureFromImage(self, image: QtGui.QImage, options: typing.Union['QQuickWindow.CreateTextureOptions', 'QQuickWindow.CreateTextureOption']) -> typing.Optional['QSGTexture']: ...
    def incubationController(self) -> typing.Optional[QtQml.QQmlIncubationController]: ...
    def renderTargetSize(self) -> QtCore.QSize: ...
    def renderTargetId(self) -> int: ...
    def renderTarget(self) -> typing.Optional[QtGui.QOpenGLFramebufferObject]: ...
    @typing.overload
    def setRenderTarget(self, fbo: typing.Optional[QtGui.QOpenGLFramebufferObject]) -> None: ...
    @typing.overload
    def setRenderTarget(self, fboId: int, size: QtCore.QSize) -> None: ...
    def grabWindow(self) -> QtGui.QImage: ...
    def sendEvent(self, a0: typing.Optional[QQuickItem], a1: typing.Optional[QtCore.QEvent]) -> bool: ...
    def mouseGrabberItem(self) -> typing.Optional[QQuickItem]: ...
    def focusObject(self) -> typing.Optional[QtCore.QObject]: ...
    def activeFocusItem(self) -> typing.Optional[QQuickItem]: ...
    def contentItem(self) -> typing.Optional[QQuickItem]: ...


class QQuickView(QQuickWindow):

    class Status(int):
        Null = ... # type: QQuickView.Status
        Ready = ... # type: QQuickView.Status
        Loading = ... # type: QQuickView.Status
        Error = ... # type: QQuickView.Status

    class ResizeMode(int):
        SizeViewToRootObject = ... # type: QQuickView.ResizeMode
        SizeRootObjectToView = ... # type: QQuickView.ResizeMode

    @typing.overload
    def __init__(self, parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...
    @typing.overload
    def __init__(self, engine: typing.Optional[QtQml.QQmlEngine], parent: typing.Optional[QtGui.QWindow]) -> None: ...
    @typing.overload
    def __init__(self, source: QtCore.QUrl, parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...

    def mouseMoveEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def keyReleaseEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def keyPressEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def timerEvent(self, a0: typing.Optional[QtCore.QTimerEvent]) -> None: ...
    def resizeEvent(self, a0: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setInitialProperties(self, initialProperties: typing.Dict[str, typing.Any]) -> None: ...
    def setSource(self, a0: QtCore.QUrl) -> None: ...
    def initialSize(self) -> QtCore.QSize: ...
    def errors(self) -> typing.List[QtQml.QQmlError]: ...
    def status(self) -> 'QQuickView.Status': ...
    def setResizeMode(self, a0: 'QQuickView.ResizeMode') -> None: ...
    def resizeMode(self) -> 'QQuickView.ResizeMode': ...
    def rootObject(self) -> typing.Optional[QQuickItem]: ...
    def rootContext(self) -> typing.Optional[QtQml.QQmlContext]: ...
    def engine(self) -> typing.Optional[QtQml.QQmlEngine]: ...
    def source(self) -> QtCore.QUrl: ...


class QQuickCloseEvent(PyQt5.sipsimplewrapper): ...


class QSGAbstractRenderer(QtCore.QObject):

    class MatrixTransformFlag(int):
        MatrixTransformFlipY = ... # type: QSGAbstractRenderer.MatrixTransformFlag

    class ClearModeBit(int):
        ClearColorBuffer = ... # type: QSGAbstractRenderer.ClearModeBit
        ClearDepthBuffer = ... # type: QSGAbstractRenderer.ClearModeBit
        ClearStencilBuffer = ... # type: QSGAbstractRenderer.ClearModeBit

    class ClearMode(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> 'QSGAbstractRenderer.ClearMode': ...
        def __xor__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> 'QSGAbstractRenderer.ClearMode': ...
        def __ior__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> 'QSGAbstractRenderer.ClearMode': ...
        def __or__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> 'QSGAbstractRenderer.ClearMode': ...
        def __iand__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> 'QSGAbstractRenderer.ClearMode': ...
        def __and__(self, f: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> 'QSGAbstractRenderer.ClearMode': ...
        def __invert__(self) -> 'QSGAbstractRenderer.ClearMode': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class MatrixTransformFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __xor__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __ior__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __or__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __iand__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __and__(self, f: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __invert__(self) -> 'QSGAbstractRenderer.MatrixTransformFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    sceneGraphChanged: typing.ClassVar[QtCore.pyqtSignal]
    def renderScene(self, fboId: int = ...) -> None: ...
    def clearMode(self) -> 'QSGAbstractRenderer.ClearMode': ...
    def setClearMode(self, mode: typing.Union['QSGAbstractRenderer.ClearMode', 'QSGAbstractRenderer.ClearModeBit']) -> None: ...
    def clearColor(self) -> QtGui.QColor: ...
    def setClearColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def projectionMatrix(self) -> QtGui.QMatrix4x4: ...
    def setProjectionMatrix(self, matrix: QtGui.QMatrix4x4) -> None: ...
    @typing.overload
    def setProjectionMatrixToRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setProjectionMatrixToRect(self, rect: QtCore.QRectF, flags: typing.Union['QSGAbstractRenderer.MatrixTransformFlags', 'QSGAbstractRenderer.MatrixTransformFlag']) -> None: ...
    def viewportRect(self) -> QtCore.QRect: ...
    @typing.overload
    def setViewportRect(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def setViewportRect(self, size: QtCore.QSize) -> None: ...
    def deviceRect(self) -> QtCore.QRect: ...
    @typing.overload
    def setDeviceRect(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def setDeviceRect(self, size: QtCore.QSize) -> None: ...


class QSGEngine(QtCore.QObject):

    class CreateTextureOption(int):
        TextureHasAlphaChannel = ... # type: QSGEngine.CreateTextureOption
        TextureOwnsGLTexture = ... # type: QSGEngine.CreateTextureOption
        TextureCanUseAtlas = ... # type: QSGEngine.CreateTextureOption
        TextureIsOpaque = ... # type: QSGEngine.CreateTextureOption

    class CreateTextureOptions(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> 'QSGEngine.CreateTextureOptions': ...
        def __xor__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> 'QSGEngine.CreateTextureOptions': ...
        def __ior__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> 'QSGEngine.CreateTextureOptions': ...
        def __or__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> 'QSGEngine.CreateTextureOptions': ...
        def __iand__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> 'QSGEngine.CreateTextureOptions': ...
        def __and__(self, f: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption']) -> 'QSGEngine.CreateTextureOptions': ...
        def __invert__(self) -> 'QSGEngine.CreateTextureOptions': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def createImageNode(self) -> typing.Optional['QSGImageNode']: ...
    def createRectangleNode(self) -> typing.Optional['QSGRectangleNode']: ...
    def rendererInterface(self) -> typing.Optional['QSGRendererInterface']: ...
    def createTextureFromId(self, id: int, size: QtCore.QSize, options: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption'] = ...) -> typing.Optional['QSGTexture']: ...
    def createTextureFromImage(self, image: QtGui.QImage, options: typing.Union['QSGEngine.CreateTextureOptions', 'QSGEngine.CreateTextureOption'] = ...) -> typing.Optional['QSGTexture']: ...
    def createRenderer(self) -> typing.Optional[QSGAbstractRenderer]: ...
    def invalidate(self) -> None: ...
    def initialize(self, context: typing.Optional[QtGui.QOpenGLContext]) -> None: ...


class QSGMaterial(PyQt5.sip.wrapper):

    class Flag(int):
        Blending = ... # type: QSGMaterial.Flag
        RequiresDeterminant = ... # type: QSGMaterial.Flag
        RequiresFullMatrixExceptTranslate = ... # type: QSGMaterial.Flag
        RequiresFullMatrix = ... # type: QSGMaterial.Flag
        CustomCompileStep = ... # type: QSGMaterial.Flag
        SupportsRhiShader = ... # type: QSGMaterial.Flag
        RhiShaderWanted = ... # type: QSGMaterial.Flag

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> 'QSGMaterial.Flags': ...
        def __xor__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> 'QSGMaterial.Flags': ...
        def __ior__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> 'QSGMaterial.Flags': ...
        def __or__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> 'QSGMaterial.Flags': ...
        def __iand__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> 'QSGMaterial.Flags': ...
        def __and__(self, f: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag']) -> 'QSGMaterial.Flags': ...
        def __invert__(self) -> 'QSGMaterial.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self) -> None: ...

    def setFlag(self, flags: typing.Union['QSGMaterial.Flags', 'QSGMaterial.Flag'], enabled: bool = ...) -> None: ...
    def flags(self) -> 'QSGMaterial.Flags': ...
    def compare(self, other: typing.Optional['QSGMaterial']) -> int: ...
    def createShader(self) -> typing.Optional['QSGMaterialShader']: ...
    def type(self) -> typing.Optional['QSGMaterialType']: ...


class QSGFlatColorMaterial(QSGMaterial):

    def __init__(self) -> None: ...

    def compare(self, other: typing.Optional[QSGMaterial]) -> int: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def createShader(self) -> typing.Optional['QSGMaterialShader']: ...
    def type(self) -> typing.Optional['QSGMaterialType']: ...


class QSGGeometry(PyQt5.sip.wrapper):

    class Type(int):
        ByteType = ... # type: QSGGeometry.Type
        UnsignedByteType = ... # type: QSGGeometry.Type
        ShortType = ... # type: QSGGeometry.Type
        UnsignedShortType = ... # type: QSGGeometry.Type
        IntType = ... # type: QSGGeometry.Type
        UnsignedIntType = ... # type: QSGGeometry.Type
        FloatType = ... # type: QSGGeometry.Type
        Bytes2Type = ... # type: QSGGeometry.Type
        Bytes3Type = ... # type: QSGGeometry.Type
        Bytes4Type = ... # type: QSGGeometry.Type
        DoubleType = ... # type: QSGGeometry.Type

    class DrawingMode(int):
        DrawPoints = ... # type: QSGGeometry.DrawingMode
        DrawLines = ... # type: QSGGeometry.DrawingMode
        DrawLineLoop = ... # type: QSGGeometry.DrawingMode
        DrawLineStrip = ... # type: QSGGeometry.DrawingMode
        DrawTriangles = ... # type: QSGGeometry.DrawingMode
        DrawTriangleStrip = ... # type: QSGGeometry.DrawingMode
        DrawTriangleFan = ... # type: QSGGeometry.DrawingMode

    class AttributeType(int):
        UnknownAttribute = ... # type: QSGGeometry.AttributeType
        PositionAttribute = ... # type: QSGGeometry.AttributeType
        ColorAttribute = ... # type: QSGGeometry.AttributeType
        TexCoordAttribute = ... # type: QSGGeometry.AttributeType
        TexCoord1Attribute = ... # type: QSGGeometry.AttributeType
        TexCoord2Attribute = ... # type: QSGGeometry.AttributeType

    class DataPattern(int):
        AlwaysUploadPattern = ... # type: QSGGeometry.DataPattern
        StreamPattern = ... # type: QSGGeometry.DataPattern
        DynamicPattern = ... # type: QSGGeometry.DataPattern
        StaticPattern = ... # type: QSGGeometry.DataPattern

    GL_POINTS = ... # type: int
    GL_LINES = ... # type: int
    GL_LINE_LOOP = ... # type: int
    GL_LINE_STRIP = ... # type: int
    GL_TRIANGLES = ... # type: int
    GL_TRIANGLE_STRIP = ... # type: int
    GL_TRIANGLE_FAN = ... # type: int

    GL_BYTE = ... # type: int
    GL_DOUBLE = ... # type: int
    GL_FLOAT = ... # type: int
    GL_INT = ... # type: int

    class Attribute(PyQt5.sipsimplewrapper):

        attributeType = ... # type: 'QSGGeometry.AttributeType'
        isVertexCoordinate = ... # type: int
        position = ... # type: int
        tupleSize = ... # type: int
        type = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.Attribute') -> None: ...

        @staticmethod
        def createWithAttributeType(pos: int, tupleSize: int, primitiveType: int, attributeType: 'QSGGeometry.AttributeType') -> 'QSGGeometry.Attribute': ...
        @staticmethod
        def create(pos: int, tupleSize: int, primitiveType: int, isPosition: bool = ...) -> 'QSGGeometry.Attribute': ...

    class AttributeSet(PyQt5.sipsimplewrapper):

        attributes = ... # type: PyQt5.sip.array
        count = ... # type: int
        stride = ... # type: int

        def __init__(self, attributes: typing.Iterable['QSGGeometry.Attribute'], stride: int = ...) -> None: ...

    class Point2D(PyQt5.sipsimplewrapper):

        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.Point2D') -> None: ...

        def set(self, nx: float, ny: float) -> None: ...

    class TexturedPoint2D(PyQt5.sipsimplewrapper):

        tx = ... # type: float
        ty = ... # type: float
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.TexturedPoint2D') -> None: ...

        def set(self, nx: float, ny: float, ntx: float, nty: float) -> None: ...

    class ColoredPoint2D(PyQt5.sipsimplewrapper):

        a = ... # type: int
        b = ... # type: int
        g = ... # type: int
        r = ... # type: int
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.ColoredPoint2D') -> None: ...

        def set(self, nx: float, ny: float, nr: int, ng: int, nb: int, na: int) -> None: ...

    def __init__(self, attribs: 'QSGGeometry.AttributeSet', vertexCount: int, indexCount: int = ..., indexType: int = ...) -> None: ...

    @staticmethod
    def updateColoredRectGeometry(g: typing.Optional['QSGGeometry'], rect: QtCore.QRectF) -> None: ...
    def sizeOfIndex(self) -> int: ...
    def vertexDataAsColoredPoint2D(self) -> PyQt5.sip.array: ...
    def vertexDataAsTexturedPoint2D(self) -> PyQt5.sip.array: ...
    def vertexDataAsPoint2D(self) -> PyQt5.sip.array: ...
    def indexDataAsUShort(self) -> PyQt5.sip.array: ...
    def indexDataAsUInt(self) -> PyQt5.sip.array: ...
    def setLineWidth(self, w: float) -> None: ...
    def lineWidth(self) -> float: ...
    def markVertexDataDirty(self) -> None: ...
    def markIndexDataDirty(self) -> None: ...
    def vertexDataPattern(self) -> 'QSGGeometry.DataPattern': ...
    def setVertexDataPattern(self, p: 'QSGGeometry.DataPattern') -> None: ...
    def indexDataPattern(self) -> 'QSGGeometry.DataPattern': ...
    def setIndexDataPattern(self, p: 'QSGGeometry.DataPattern') -> None: ...
    @staticmethod
    def updateTexturedRectGeometry(g: typing.Optional['QSGGeometry'], rect: QtCore.QRectF, sourceRect: QtCore.QRectF) -> None: ...
    @staticmethod
    def updateRectGeometry(g: typing.Optional['QSGGeometry'], rect: QtCore.QRectF) -> None: ...
    def sizeOfVertex(self) -> int: ...
    def attributes(self) -> PyQt5.sip.array: ...
    def attributeCount(self) -> int: ...
    def indexData(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def indexCount(self) -> int: ...
    def indexType(self) -> int: ...
    def vertexData(self) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def vertexCount(self) -> int: ...
    def allocate(self, vertexCount: int, indexCount: int = ...) -> None: ...
    def drawingMode(self) -> int: ...
    def setDrawingMode(self, mode: int) -> None: ...
    @staticmethod
    def defaultAttributes_ColoredPoint2D() -> 'QSGGeometry.AttributeSet': ...
    @staticmethod
    def defaultAttributes_TexturedPoint2D() -> 'QSGGeometry.AttributeSet': ...
    @staticmethod
    def defaultAttributes_Point2D() -> 'QSGGeometry.AttributeSet': ...


class QSGNode(PyQt5.sip.wrapper):

    class DirtyStateBit(int):
        DirtyMatrix = ... # type: QSGNode.DirtyStateBit
        DirtyNodeAdded = ... # type: QSGNode.DirtyStateBit
        DirtyNodeRemoved = ... # type: QSGNode.DirtyStateBit
        DirtyGeometry = ... # type: QSGNode.DirtyStateBit
        DirtyMaterial = ... # type: QSGNode.DirtyStateBit
        DirtyOpacity = ... # type: QSGNode.DirtyStateBit

    class Flag(int):
        OwnedByParent = ... # type: QSGNode.Flag
        UsePreprocess = ... # type: QSGNode.Flag
        OwnsGeometry = ... # type: QSGNode.Flag
        OwnsMaterial = ... # type: QSGNode.Flag
        OwnsOpaqueMaterial = ... # type: QSGNode.Flag

    class NodeType(int):
        BasicNodeType = ... # type: QSGNode.NodeType
        GeometryNodeType = ... # type: QSGNode.NodeType
        TransformNodeType = ... # type: QSGNode.NodeType
        ClipNodeType = ... # type: QSGNode.NodeType
        OpacityNodeType = ... # type: QSGNode.NodeType

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> 'QSGNode.Flags': ...
        def __xor__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> 'QSGNode.Flags': ...
        def __ior__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> 'QSGNode.Flags': ...
        def __or__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> 'QSGNode.Flags': ...
        def __iand__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> 'QSGNode.Flags': ...
        def __and__(self, f: typing.Union['QSGNode.Flags', 'QSGNode.Flag']) -> 'QSGNode.Flags': ...
        def __invert__(self) -> 'QSGNode.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class DirtyState(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> 'QSGNode.DirtyState': ...
        def __xor__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> 'QSGNode.DirtyState': ...
        def __ior__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> 'QSGNode.DirtyState': ...
        def __or__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> 'QSGNode.DirtyState': ...
        def __iand__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> 'QSGNode.DirtyState': ...
        def __and__(self, f: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> 'QSGNode.DirtyState': ...
        def __invert__(self) -> 'QSGNode.DirtyState': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self) -> None: ...

    def preprocess(self) -> None: ...
    def setFlags(self, a0: typing.Union['QSGNode.Flags', 'QSGNode.Flag'], enabled: bool = ...) -> None: ...
    def setFlag(self, a0: 'QSGNode.Flag', enabled: bool = ...) -> None: ...
    def flags(self) -> 'QSGNode.Flags': ...
    def isSubtreeBlocked(self) -> bool: ...
    def markDirty(self, bits: typing.Union['QSGNode.DirtyState', 'QSGNode.DirtyStateBit']) -> None: ...
    def type(self) -> 'QSGNode.NodeType': ...
    def previousSibling(self) -> typing.Optional['QSGNode']: ...
    def nextSibling(self) -> typing.Optional['QSGNode']: ...
    def lastChild(self) -> typing.Optional['QSGNode']: ...
    def firstChild(self) -> typing.Optional['QSGNode']: ...
    def childAtIndex(self, i: int) -> typing.Optional['QSGNode']: ...
    def __len__(self) -> int: ...
    def childCount(self) -> int: ...
    def insertChildNodeAfter(self, node: typing.Optional['QSGNode'], after: typing.Optional['QSGNode']) -> None: ...
    def insertChildNodeBefore(self, node: typing.Optional['QSGNode'], before: typing.Optional['QSGNode']) -> None: ...
    def appendChildNode(self, node: typing.Optional['QSGNode']) -> None: ...
    def prependChildNode(self, node: typing.Optional['QSGNode']) -> None: ...
    def removeAllChildNodes(self) -> None: ...
    def removeChildNode(self, node: typing.Optional['QSGNode']) -> None: ...
    def parent(self) -> typing.Optional['QSGNode']: ...


class QSGBasicGeometryNode(QSGNode):

    def geometry(self) -> typing.Optional[QSGGeometry]: ...
    def setGeometry(self, geometry: typing.Optional[QSGGeometry]) -> None: ...


class QSGGeometryNode(QSGBasicGeometryNode):

    def __init__(self) -> None: ...

    def opaqueMaterial(self) -> typing.Optional[QSGMaterial]: ...
    def setOpaqueMaterial(self, material: typing.Optional[QSGMaterial]) -> None: ...
    def material(self) -> typing.Optional[QSGMaterial]: ...
    def setMaterial(self, material: typing.Optional[QSGMaterial]) -> None: ...


class QSGImageNode(QSGGeometryNode):

    class TextureCoordinatesTransformFlag(int):
        NoTransform = ... # type: QSGImageNode.TextureCoordinatesTransformFlag
        MirrorHorizontally = ... # type: QSGImageNode.TextureCoordinatesTransformFlag
        MirrorVertically = ... # type: QSGImageNode.TextureCoordinatesTransformFlag

    class TextureCoordinatesTransformMode(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __xor__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __ior__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __or__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __iand__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __and__(self, f: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __invert__(self) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @staticmethod
    def rebuildGeometry(g: typing.Optional[QSGGeometry], texture: typing.Optional['QSGTexture'], rect: QtCore.QRectF, sourceRect: QtCore.QRectF, texCoordMode: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> None: ...
    def ownsTexture(self) -> bool: ...
    def setOwnsTexture(self, owns: bool) -> None: ...
    def textureCoordinatesTransform(self) -> 'QSGImageNode.TextureCoordinatesTransformMode': ...
    def setTextureCoordinatesTransform(self, mode: typing.Union['QSGImageNode.TextureCoordinatesTransformMode', 'QSGImageNode.TextureCoordinatesTransformFlag']) -> None: ...
    def mipmapFiltering(self) -> 'QSGTexture.Filtering': ...
    def setMipmapFiltering(self, filtering: 'QSGTexture.Filtering') -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, filtering: 'QSGTexture.Filtering') -> None: ...
    def texture(self) -> typing.Optional['QSGTexture']: ...
    def setTexture(self, texture: typing.Optional['QSGTexture']) -> None: ...
    def sourceRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setSourceRect(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def setSourceRect(self, x: float, y: float, w: float, h: float) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGMaterialShader(PyQt5.sip.wrapper):

    class RenderState(PyQt5.sipsimplewrapper):

        class DirtyState(int):
            DirtyMatrix = ... # type: QSGMaterialShader.RenderState.DirtyState
            DirtyOpacity = ... # type: QSGMaterialShader.RenderState.DirtyState
            DirtyCachedMaterialData = ... # type: QSGMaterialShader.RenderState.DirtyState
            DirtyAll = ... # type: QSGMaterialShader.RenderState.DirtyState

        class DirtyStates(PyQt5.sipsimplewrapper):

            @typing.overload
            def __init__(self) -> None: ...
            @typing.overload
            def __init__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> None: ...

            def __hash__(self) -> int: ...
            def __bool__(self) -> int: ...
            def __ne__(self, other: object): ...
            def __eq__(self, other: object): ...
            def __ixor__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __xor__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __ior__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __or__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __iand__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __and__(self, f: typing.Union['QSGMaterialShader.RenderState.DirtyStates', 'QSGMaterialShader.RenderState.DirtyState']) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __invert__(self) -> 'QSGMaterialShader.RenderState.DirtyStates': ...
            def __index__(self) -> int: ...
            def __int__(self) -> int: ...

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGMaterialShader.RenderState') -> None: ...

        def isCachedMaterialDataDirty(self) -> bool: ...
        def devicePixelRatio(self) -> float: ...
        def projectionMatrix(self) -> QtGui.QMatrix4x4: ...
        def context(self) -> typing.Optional[QtGui.QOpenGLContext]: ...
        def determinant(self) -> float: ...
        def deviceRect(self) -> QtCore.QRect: ...
        def viewportRect(self) -> QtCore.QRect: ...
        def modelViewMatrix(self) -> QtGui.QMatrix4x4: ...
        def combinedMatrix(self) -> QtGui.QMatrix4x4: ...
        def opacity(self) -> float: ...
        def isOpacityDirty(self) -> bool: ...
        def isMatrixDirty(self) -> bool: ...
        def dirtyStates(self) -> 'QSGMaterialShader.RenderState.DirtyStates': ...

    def __init__(self) -> None: ...

    def setShaderSourceFiles(self, type: typing.Union[QtGui.QOpenGLShader.ShaderType, QtGui.QOpenGLShader.ShaderTypeBit], sourceFiles: typing.Iterable[typing.Optional[str]]) -> None: ...
    def setShaderSourceFile(self, type: typing.Union[QtGui.QOpenGLShader.ShaderType, QtGui.QOpenGLShader.ShaderTypeBit], sourceFile: typing.Optional[str]) -> None: ...
    def fragmentShader(self) -> typing.Optional[str]: ...
    def vertexShader(self) -> typing.Optional[str]: ...
    def initialize(self) -> None: ...
    def compile(self) -> None: ...
    def program(self) -> typing.Optional[QtGui.QOpenGLShaderProgram]: ...
    def attributeNames(self) -> typing.List[str]: ...
    def updateState(self, state: 'QSGMaterialShader.RenderState', newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> None: ...
    def deactivate(self) -> None: ...
    def activate(self) -> None: ...


class QSGMaterialType(PyQt5.sipsimplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSGMaterialType') -> None: ...


class QSGMaterialRhiShader(QSGMaterialShader):

    class Flag(int):
        UpdatesGraphicsPipelineState = ... # type: QSGMaterialRhiShader.Flag

    class RenderState(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGMaterialRhiShader.RenderState') -> None: ...

        def uniformData(self) -> typing.Optional[QtCore.QByteArray]: ...
        def devicePixelRatio(self) -> float: ...
        def determinant(self) -> float: ...
        def deviceRect(self) -> QtCore.QRect: ...
        def viewportRect(self) -> QtCore.QRect: ...
        def projectionMatrix(self) -> QtGui.QMatrix4x4: ...
        def modelViewMatrix(self) -> QtGui.QMatrix4x4: ...
        def combinedMatrix(self) -> QtGui.QMatrix4x4: ...
        def opacity(self) -> float: ...
        def isOpacityDirty(self) -> bool: ...
        def isMatrixDirty(self) -> bool: ...
        def dirtyStates(self) -> QSGMaterialShader.RenderState.DirtyStates: ...

    class GraphicsPipelineState(PyQt5.sipsimplewrapper):

        class CullMode(int):
            CullNone = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.CullMode
            CullFront = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.CullMode
            CullBack = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.CullMode

        class ColorMaskComponent(int):
            R = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent
            G = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent
            B = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent
            A = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent

        class BlendFactor(int):
            Zero = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            One = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            SrcColor = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusSrcColor = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            DstColor = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusDstColor = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            SrcAlpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusSrcAlpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            DstAlpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusDstAlpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            ConstantColor = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusConstantColor = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            ConstantAlpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusConstantAlpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            SrcAlphaSaturate = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            Src1Color = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusSrc1Color = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            Src1Alpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor
            OneMinusSrc1Alpha = ... # type: QSGMaterialRhiShader.GraphicsPipelineState.BlendFactor

        class ColorMask(PyQt5.sipsimplewrapper):

            @typing.overload
            def __init__(self) -> None: ...
            @typing.overload
            def __init__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> None: ...

            def __hash__(self) -> int: ...
            def __bool__(self) -> int: ...
            def __ne__(self, other: object): ...
            def __eq__(self, other: object): ...
            def __ixor__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __xor__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __ior__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __or__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __iand__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __and__(self, f: typing.Union['QSGMaterialRhiShader.GraphicsPipelineState.ColorMask', 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMaskComponent']) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __invert__(self) -> 'QSGMaterialRhiShader.GraphicsPipelineState.ColorMask': ...
            def __index__(self) -> int: ...
            def __int__(self) -> int: ...

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGMaterialRhiShader.GraphicsPipelineState') -> None: ...

    class Flags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> 'QSGMaterialRhiShader.Flags': ...
        def __xor__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> 'QSGMaterialRhiShader.Flags': ...
        def __ior__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> 'QSGMaterialRhiShader.Flags': ...
        def __or__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> 'QSGMaterialRhiShader.Flags': ...
        def __iand__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> 'QSGMaterialRhiShader.Flags': ...
        def __and__(self, f: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag']) -> 'QSGMaterialRhiShader.Flags': ...
        def __invert__(self) -> 'QSGMaterialRhiShader.Flags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self) -> None: ...

    def setFlag(self, flags: typing.Union['QSGMaterialRhiShader.Flags', 'QSGMaterialRhiShader.Flag'], on: bool = ...) -> None: ...
    def flags(self) -> 'QSGMaterialRhiShader.Flags': ...
    def updateGraphicsPipelineState(self, state: 'QSGMaterialRhiShader.RenderState', ps: typing.Optional['QSGMaterialRhiShader.GraphicsPipelineState'], newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> bool: ...
    def updateSampledImage(self, state: 'QSGMaterialRhiShader.RenderState', binding: int, newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> typing.Optional['QSGTexture']: ...
    def updateUniformData(self, state: 'QSGMaterialRhiShader.RenderState', newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> bool: ...


class QSGClipNode(QSGBasicGeometryNode):

    def __init__(self) -> None: ...

    def clipRect(self) -> QtCore.QRectF: ...
    def setClipRect(self, a0: QtCore.QRectF) -> None: ...
    def isRectangular(self) -> bool: ...
    def setIsRectangular(self, rectHint: bool) -> None: ...


class QSGTransformNode(QSGNode):

    def __init__(self) -> None: ...

    def matrix(self) -> QtGui.QMatrix4x4: ...
    def setMatrix(self, matrix: QtGui.QMatrix4x4) -> None: ...


class QSGOpacityNode(QSGNode):

    def __init__(self) -> None: ...

    def opacity(self) -> float: ...
    def setOpacity(self, opacity: float) -> None: ...


class QSGRectangleNode(QSGGeometryNode):

    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGRendererInterface(PyQt5.sipsimplewrapper):

    class ShaderSourceType(int):
        ShaderSourceString = ... # type: QSGRendererInterface.ShaderSourceType
        ShaderSourceFile = ... # type: QSGRendererInterface.ShaderSourceType
        ShaderByteCode = ... # type: QSGRendererInterface.ShaderSourceType

    class ShaderCompilationType(int):
        RuntimeCompilation = ... # type: QSGRendererInterface.ShaderCompilationType
        OfflineCompilation = ... # type: QSGRendererInterface.ShaderCompilationType

    class ShaderType(int):
        UnknownShadingLanguage = ... # type: QSGRendererInterface.ShaderType
        GLSL = ... # type: QSGRendererInterface.ShaderType
        HLSL = ... # type: QSGRendererInterface.ShaderType
        RhiShader = ... # type: QSGRendererInterface.ShaderType

    class Resource(int):
        DeviceResource = ... # type: QSGRendererInterface.Resource
        CommandQueueResource = ... # type: QSGRendererInterface.Resource
        CommandListResource = ... # type: QSGRendererInterface.Resource
        PainterResource = ... # type: QSGRendererInterface.Resource
        RhiResource = ... # type: QSGRendererInterface.Resource
        PhysicalDeviceResource = ... # type: QSGRendererInterface.Resource
        OpenGLContextResource = ... # type: QSGRendererInterface.Resource
        DeviceContextResource = ... # type: QSGRendererInterface.Resource
        CommandEncoderResource = ... # type: QSGRendererInterface.Resource
        VulkanInstanceResource = ... # type: QSGRendererInterface.Resource
        RenderPassResource = ... # type: QSGRendererInterface.Resource

    class GraphicsApi(int):
        Unknown = ... # type: QSGRendererInterface.GraphicsApi
        Software = ... # type: QSGRendererInterface.GraphicsApi
        OpenGL = ... # type: QSGRendererInterface.GraphicsApi
        Direct3D12 = ... # type: QSGRendererInterface.GraphicsApi
        OpenVG = ... # type: QSGRendererInterface.GraphicsApi
        OpenGLRhi = ... # type: QSGRendererInterface.GraphicsApi
        Direct3D11Rhi = ... # type: QSGRendererInterface.GraphicsApi
        VulkanRhi = ... # type: QSGRendererInterface.GraphicsApi
        MetalRhi = ... # type: QSGRendererInterface.GraphicsApi
        NullRhi = ... # type: QSGRendererInterface.GraphicsApi

    class ShaderCompilationTypes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __xor__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __ior__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __or__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __iand__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __and__(self, f: typing.Union['QSGRendererInterface.ShaderCompilationTypes', 'QSGRendererInterface.ShaderCompilationType']) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __invert__(self) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class ShaderSourceTypes(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __xor__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __ior__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __or__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __iand__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __and__(self, f: typing.Union['QSGRendererInterface.ShaderSourceTypes', 'QSGRendererInterface.ShaderSourceType']) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __invert__(self) -> 'QSGRendererInterface.ShaderSourceTypes': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    @staticmethod
    def isApiRhiBased(api: 'QSGRendererInterface.GraphicsApi') -> bool: ...
    def shaderSourceType(self) -> 'QSGRendererInterface.ShaderSourceTypes': ...
    def shaderCompilationType(self) -> 'QSGRendererInterface.ShaderCompilationTypes': ...
    def shaderType(self) -> 'QSGRendererInterface.ShaderType': ...
    @typing.overload
    def getResource(self, window: typing.Optional[QQuickWindow], resource: 'QSGRendererInterface.Resource') -> typing.Optional[PyQt5.sip.voidptr]: ...
    @typing.overload
    def getResource(self, window: typing.Optional[QQuickWindow], resource: typing.Optional[str]) -> typing.Optional[PyQt5.sip.voidptr]: ...
    def graphicsApi(self) -> 'QSGRendererInterface.GraphicsApi': ...


class QSGRenderNode(QSGNode):

    class RenderingFlag(int):
        BoundedRectRendering = ... # type: QSGRenderNode.RenderingFlag
        DepthAwareRendering = ... # type: QSGRenderNode.RenderingFlag
        OpaqueRendering = ... # type: QSGRenderNode.RenderingFlag

    class StateFlag(int):
        DepthState = ... # type: QSGRenderNode.StateFlag
        StencilState = ... # type: QSGRenderNode.StateFlag
        ScissorState = ... # type: QSGRenderNode.StateFlag
        ColorState = ... # type: QSGRenderNode.StateFlag
        BlendState = ... # type: QSGRenderNode.StateFlag
        CullState = ... # type: QSGRenderNode.StateFlag
        ViewportState = ... # type: QSGRenderNode.StateFlag
        RenderTargetState = ... # type: QSGRenderNode.StateFlag

    class StateFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> 'QSGRenderNode.StateFlags': ...
        def __xor__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> 'QSGRenderNode.StateFlags': ...
        def __ior__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> 'QSGRenderNode.StateFlags': ...
        def __or__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> 'QSGRenderNode.StateFlags': ...
        def __iand__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> 'QSGRenderNode.StateFlags': ...
        def __and__(self, f: typing.Union['QSGRenderNode.StateFlags', 'QSGRenderNode.StateFlag']) -> 'QSGRenderNode.StateFlags': ...
        def __invert__(self) -> 'QSGRenderNode.StateFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class RenderingFlags(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> 'QSGRenderNode.RenderingFlags': ...
        def __xor__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> 'QSGRenderNode.RenderingFlags': ...
        def __ior__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> 'QSGRenderNode.RenderingFlags': ...
        def __or__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> 'QSGRenderNode.RenderingFlags': ...
        def __iand__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> 'QSGRenderNode.RenderingFlags': ...
        def __and__(self, f: typing.Union['QSGRenderNode.RenderingFlags', 'QSGRenderNode.RenderingFlag']) -> 'QSGRenderNode.RenderingFlags': ...
        def __invert__(self) -> 'QSGRenderNode.RenderingFlags': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    class RenderState(PyQt5.sipsimplewrapper):

        def get(self, state: typing.Optional[str]) -> typing.Optional[PyQt5.sip.voidptr]: ...
        def clipRegion(self) -> typing.Optional[QtGui.QRegion]: ...
        def stencilEnabled(self) -> bool: ...
        def stencilValue(self) -> int: ...
        def scissorEnabled(self) -> bool: ...
        def scissorRect(self) -> QtCore.QRect: ...
        def projectionMatrix(self) -> typing.Optional[QtGui.QMatrix4x4]: ...

    def __init__(self) -> None: ...

    def inheritedOpacity(self) -> float: ...
    def clipList(self) -> typing.Optional[QSGClipNode]: ...
    def matrix(self) -> typing.Optional[QtGui.QMatrix4x4]: ...
    def rect(self) -> QtCore.QRectF: ...
    def flags(self) -> 'QSGRenderNode.RenderingFlags': ...
    def releaseResources(self) -> None: ...
    def render(self, state: typing.Optional['QSGRenderNode.RenderState']) -> None: ...
    def changedStates(self) -> 'QSGRenderNode.StateFlags': ...


class QSGSimpleRectNode(QSGGeometryNode):

    @typing.overload
    def __init__(self, rect: QtCore.QRectF, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    @typing.overload
    def __init__(self) -> None: ...

    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor]) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGSimpleTextureNode(QSGGeometryNode):

    class TextureCoordinatesTransformFlag(int):
        NoTransform = ... # type: QSGSimpleTextureNode.TextureCoordinatesTransformFlag
        MirrorHorizontally = ... # type: QSGSimpleTextureNode.TextureCoordinatesTransformFlag
        MirrorVertically = ... # type: QSGSimpleTextureNode.TextureCoordinatesTransformFlag

    class TextureCoordinatesTransformMode(PyQt5.sipsimplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> None: ...

        def __hash__(self) -> int: ...
        def __bool__(self) -> int: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ixor__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __xor__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __ior__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __or__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __iand__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __and__(self, f: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __invert__(self) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...

    def __init__(self) -> None: ...

    def sourceRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setSourceRect(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def setSourceRect(self, x: float, y: float, w: float, h: float) -> None: ...
    def ownsTexture(self) -> bool: ...
    def setOwnsTexture(self, owns: bool) -> None: ...
    def textureCoordinatesTransform(self) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformMode': ...
    def setTextureCoordinatesTransform(self, mode: typing.Union['QSGSimpleTextureNode.TextureCoordinatesTransformMode', 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag']) -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, filtering: 'QSGTexture.Filtering') -> None: ...
    def texture(self) -> typing.Optional['QSGTexture']: ...
    def setTexture(self, texture: typing.Optional['QSGTexture']) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGTexture(QtCore.QObject):

    class AnisotropyLevel(int):
        AnisotropyNone = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy2x = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy4x = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy8x = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy16x = ... # type: QSGTexture.AnisotropyLevel

    class Filtering(int):
        None_ = ... # type: QSGTexture.Filtering
        Nearest = ... # type: QSGTexture.Filtering
        Linear = ... # type: QSGTexture.Filtering

    class WrapMode(int):
        Repeat = ... # type: QSGTexture.WrapMode
        ClampToEdge = ... # type: QSGTexture.WrapMode
        MirroredRepeat = ... # type: QSGTexture.WrapMode

    class NativeTexture(PyQt5.sipsimplewrapper):

        layout = ... # type: int
        object = ... # type: PyQt5.sip.voidptr

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGTexture.NativeTexture') -> None: ...

    def __init__(self) -> None: ...

    def nativeTexture(self) -> 'QSGTexture.NativeTexture': ...
    def comparisonKey(self) -> int: ...
    def anisotropyLevel(self) -> 'QSGTexture.AnisotropyLevel': ...
    def setAnisotropyLevel(self, level: 'QSGTexture.AnisotropyLevel') -> None: ...
    def convertToNormalizedSourceRect(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def verticalWrapMode(self) -> 'QSGTexture.WrapMode': ...
    def setVerticalWrapMode(self, vwrap: 'QSGTexture.WrapMode') -> None: ...
    def horizontalWrapMode(self) -> 'QSGTexture.WrapMode': ...
    def setHorizontalWrapMode(self, hwrap: 'QSGTexture.WrapMode') -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, filter: 'QSGTexture.Filtering') -> None: ...
    def mipmapFiltering(self) -> 'QSGTexture.Filtering': ...
    def setMipmapFiltering(self, filter: 'QSGTexture.Filtering') -> None: ...
    def updateBindOptions(self, force: bool = ...) -> None: ...
    def bind(self) -> None: ...
    def removedFromAtlas(self) -> typing.Optional['QSGTexture']: ...
    def isAtlasTexture(self) -> bool: ...
    def normalizedTextureSubRect(self) -> QtCore.QRectF: ...
    def hasMipmaps(self) -> bool: ...
    def hasAlphaChannel(self) -> bool: ...
    def textureSize(self) -> QtCore.QSize: ...
    def textureId(self) -> int: ...


class QSGDynamicTexture(QSGTexture):

    def __init__(self) -> None: ...

    def updateTexture(self) -> bool: ...


class QSGOpaqueTextureMaterial(QSGMaterial):

    def __init__(self) -> None: ...

    def anisotropyLevel(self) -> QSGTexture.AnisotropyLevel: ...
    def setAnisotropyLevel(self, level: QSGTexture.AnisotropyLevel) -> None: ...
    def verticalWrapMode(self) -> QSGTexture.WrapMode: ...
    def setVerticalWrapMode(self, mode: QSGTexture.WrapMode) -> None: ...
    def horizontalWrapMode(self) -> QSGTexture.WrapMode: ...
    def setHorizontalWrapMode(self, mode: QSGTexture.WrapMode) -> None: ...
    def filtering(self) -> QSGTexture.Filtering: ...
    def setFiltering(self, filtering: QSGTexture.Filtering) -> None: ...
    def mipmapFiltering(self) -> QSGTexture.Filtering: ...
    def setMipmapFiltering(self, filtering: QSGTexture.Filtering) -> None: ...
    def texture(self) -> typing.Optional[QSGTexture]: ...
    def setTexture(self, texture: typing.Optional[QSGTexture]) -> None: ...
    def compare(self, other: typing.Optional[QSGMaterial]) -> int: ...
    def createShader(self) -> typing.Optional[QSGMaterialShader]: ...
    def type(self) -> typing.Optional[QSGMaterialType]: ...


class QSGTextureMaterial(QSGOpaqueTextureMaterial):

    def __init__(self) -> None: ...

    def createShader(self) -> typing.Optional[QSGMaterialShader]: ...
    def type(self) -> typing.Optional[QSGMaterialType]: ...


class QSGTextureProvider(QtCore.QObject):

    def __init__(self) -> None: ...

    textureChanged: typing.ClassVar[QtCore.pyqtSignal]
    def texture(self) -> typing.Optional[QSGTexture]: ...


class QSGVertexColorMaterial(QSGMaterial):

    def __init__(self) -> None: ...

    def createShader(self) -> typing.Optional[QSGMaterialShader]: ...
    def type(self) -> typing.Optional[QSGMaterialType]: ...
    def compare(self, other: typing.Optional[QSGMaterial]) -> int: ...
