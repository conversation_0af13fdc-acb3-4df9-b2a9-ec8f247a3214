import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtNfc 5.15'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QDeclarativeNdefFilter"
        prototype: "QObject"
        exports: ["QtNfc/NdefFilter 5.0", "QtNfc/NdefFilter 5.2"]
        exportMetaObjectRevisions: [0, 0]
        Property { name: "type"; type: "string" }
        Property { name: "typeNameFormat"; type: "QQmlNdefRecord::TypeNameFormat" }
        Property { name: "minimum"; type: "int" }
        Property { name: "maximum"; type: "int" }
    }
    Component {
        name: "QDeclarativeNdefMimeRecord"
        prototype: "QQmlNdefRecord"
        exports: ["QtNfc/NdefMimeRecord 5.0", "QtNfc/NdefMimeRecord 5.2"]
        exportMetaObjectRevisions: [0, 0]
        Property { name: "uri"; type: "string"; isReadonly: true }
    }
    Component {
        name: "QDeclarativeNdefTextRecord"
        prototype: "QQmlNdefRecord"
        exports: ["QtNfc/NdefTextRecord 5.0", "QtNfc/NdefTextRecord 5.2"]
        exportMetaObjectRevisions: [0, 0]
        Enum {
            name: "LocaleMatch"
            values: {
                "LocaleMatchedNone": 0,
                "LocaleMatchedEnglish": 1,
                "LocaleMatchedLanguage": 2,
                "LocaleMatchedLanguageAndCountry": 3
            }
        }
        Property { name: "text"; type: "string" }
        Property { name: "locale"; type: "string" }
        Property { name: "localeMatch"; type: "LocaleMatch"; isReadonly: true }
    }
    Component {
        name: "QDeclarativeNdefUriRecord"
        prototype: "QQmlNdefRecord"
        exports: ["QtNfc/NdefUriRecord 5.0", "QtNfc/NdefUriRecord 5.2"]
        exportMetaObjectRevisions: [0, 0]
        Property { name: "uri"; type: "string" }
    }
    Component {
        name: "QDeclarativeNearField"
        prototype: "QObject"
        exports: [
            "QtNfc/NearField 5.0",
            "QtNfc/NearField 5.2",
            "QtNfc/NearField 5.4",
            "QtNfc/NearField 5.5"
        ]
        exportMetaObjectRevisions: [0, 0, 0, 1]
        Property { name: "messageRecords"; type: "QQmlNdefRecord"; isList: true; isReadonly: true }
        Property { name: "filter"; type: "QDeclarativeNdefFilter"; isList: true; isReadonly: true }
        Property { name: "orderMatch"; type: "bool" }
        Property { name: "polling"; revision: 1; type: "bool" }
        Signal { name: "pollingChanged"; revision: 1 }
        Signal { name: "tagFound"; revision: 1 }
        Signal { name: "tagRemoved"; revision: 1 }
    }
    Component {
        name: "QQmlNdefRecord"
        prototype: "QObject"
        exports: ["QtNfc/NdefRecord 5.0", "QtNfc/NdefRecord 5.2"]
        exportMetaObjectRevisions: [0, 0]
        Enum {
            name: "TypeNameFormat"
            values: {
                "Empty": 0,
                "NfcRtd": 1,
                "Mime": 2,
                "Uri": 3,
                "ExternalRtd": 4,
                "Unknown": 5
            }
        }
        Property { name: "type"; type: "string" }
        Property { name: "typeNameFormat"; type: "TypeNameFormat" }
        Property { name: "record"; type: "QNdefRecord" }
    }
}
