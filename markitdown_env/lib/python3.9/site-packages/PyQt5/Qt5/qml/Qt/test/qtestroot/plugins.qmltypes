import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "private/quicktest_p.h"
        name: "QTestRootObject"
        prototype: "QObject"
        exports: ["Qt.test.qtestroot/QTestRootObject 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Property { name: "windowShown"; type: "bool"; isReadonly: true }
        Property { name: "hasTestCase"; type: "bool" }
        Property { name: "defined"; type: "QObject"; isReadonly: true; isPointer: true }
        Method { name: "quit" }
    }
}
