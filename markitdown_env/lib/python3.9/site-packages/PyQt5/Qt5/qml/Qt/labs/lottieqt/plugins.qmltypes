import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable Qt.labs.lottieqt 1.0'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "BMLiteral"
        prototype: "QObject"
        exports: ["Qt.labs.lottieqt/BMPropertyType 1.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "PropertyType"
            values: {
                "RectPosition": 0,
                "RectSize": 1,
                "RectRoundness": 2
            }
        }
    }
    Component {
        name: "LottieAnimation"
        defaultProperty: "data"
        prototype: "QQuickPaintedItem"
        exports: ["Qt.labs.lottieqt/LottieAnimation 1.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Loading": 1,
                "Ready": 2,
                "Error": 3
            }
        }
        Enum {
            name: "Quality"
            values: {
                "LowQuality": 0,
                "MediumQuality": 1,
                "HighQuality": 2
            }
        }
        Enum {
            name: "Direction"
            values: {
                "Forward": 1,
                "Reverse": -1
            }
        }
        Enum {
            name: "LoopCount"
            values: {
                "Infinite": -1
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "frameRate"; type: "int" }
        Property { name: "startFrame"; type: "int"; isReadonly: true }
        Property { name: "endFrame"; type: "int"; isReadonly: true }
        Property { name: "status"; type: "Status" }
        Property { name: "quality"; type: "Quality" }
        Property { name: "autoPlay"; type: "bool" }
        Property { name: "loops"; type: "int" }
        Property { name: "direction"; type: "Direction" }
        Signal { name: "finished" }
        Method { name: "start" }
        Method { name: "play" }
        Method { name: "pause" }
        Method { name: "togglePause" }
        Method { name: "stop" }
        Method {
            name: "gotoAndPlay"
            Parameter { name: "frame"; type: "int" }
        }
        Method {
            name: "gotoAndPlay"
            type: "bool"
            Parameter { name: "frameMarker"; type: "string" }
        }
        Method {
            name: "gotoAndStop"
            Parameter { name: "frame"; type: "int" }
        }
        Method {
            name: "gotoAndStop"
            type: "bool"
            Parameter { name: "frameMarker"; type: "string" }
        }
        Method {
            name: "getDuration"
            type: "double"
            Parameter { name: "inFrames"; type: "bool" }
        }
        Method { name: "getDuration"; type: "double" }
    }
}
