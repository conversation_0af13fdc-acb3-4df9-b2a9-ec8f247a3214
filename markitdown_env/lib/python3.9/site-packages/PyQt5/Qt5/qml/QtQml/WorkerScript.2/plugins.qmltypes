import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        file: "private/qquickworkerscript_p.h"
        name: "QQuickWorkerScript"
        prototype: "QObject"
        exports: [
            "QtQml.WorkerScript/WorkerScript 2.0",
            "QtQml.WorkerScript/WorkerScript 2.15"
        ]
        exportMetaObjectRevisions: [0, 15]
        Property { name: "source"; type: "QUrl" }
        Property { name: "ready"; revision: 15; type: "bool"; isReadonly: true }
        Signal { name: "readyChanged"; revision: 15 }
        Signal {
            name: "message"
            Parameter { name: "messageObject"; type: "QJSValue" }
        }
        Method {
            name: "sendMessage"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
}
