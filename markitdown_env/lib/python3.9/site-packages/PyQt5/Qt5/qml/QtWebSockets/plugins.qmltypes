import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtWebSockets 1.15'

Module {
    dependencies: []
    Component {
        name: "QQmlWebSocket"
        prototype: "QObject"
        exports: ["QtWebSockets/WebSocket 1.0", "QtWebSockets/WebSocket 1.1"]
        exportMetaObjectRevisions: [0, 1]
        Enum {
            name: "Status"
            values: {
                "Connecting": 0,
                "Open": 1,
                "Closing": 2,
                "Closed": 3,
                "Error": 4
            }
        }
        Property { name: "url"; type: "QUrl" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "active"; type: "bool" }
        Signal {
            name: "textMessageReceived"
            Parameter { name: "message"; type: "string" }
        }
        Signal {
            name: "binaryMessageReceived"
            revision: 1
            Parameter { name: "message"; type: "QByteArray" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "QQmlWebSocket::Status" }
        }
        Signal {
            name: "activeChanged"
            Parameter { name: "isActive"; type: "bool" }
        }
        Signal {
            name: "errorStringChanged"
            Parameter { name: "errorString"; type: "string" }
        }
        Method {
            name: "sendTextMessage"
            type: "qlonglong"
            Parameter { name: "message"; type: "string" }
        }
        Method {
            name: "sendBinaryMessage"
            revision: 1
            type: "qlonglong"
            Parameter { name: "message"; type: "QByteArray" }
        }
    }
    Component {
        name: "QQmlWebSocketServer"
        prototype: "QObject"
        exports: ["QtWebSockets/WebSocketServer 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Property { name: "host"; type: "string" }
        Property { name: "port"; type: "int" }
        Property { name: "name"; type: "string" }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "listen"; type: "bool" }
        Property { name: "accept"; type: "bool" }
        Signal {
            name: "clientConnected"
            Parameter { name: "webSocket"; type: "QQmlWebSocket"; isPointer: true }
        }
        Signal {
            name: "errorStringChanged"
            Parameter { name: "errorString"; type: "string" }
        }
        Signal {
            name: "urlChanged"
            Parameter { name: "url"; type: "QUrl" }
        }
        Signal {
            name: "portChanged"
            Parameter { name: "port"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "hostChanged"
            Parameter { name: "host"; type: "string" }
        }
        Signal {
            name: "listenChanged"
            Parameter { name: "listen"; type: "bool" }
        }
        Signal {
            name: "acceptChanged"
            Parameter { name: "accept"; type: "bool" }
        }
    }
}
