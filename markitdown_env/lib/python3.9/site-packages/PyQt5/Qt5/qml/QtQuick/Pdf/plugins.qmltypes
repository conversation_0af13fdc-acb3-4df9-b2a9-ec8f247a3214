import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick.Pdf 5.14'

Module {
    dependencies: [
        "QtGraphicalEffects 1.12",
        "QtQuick 2.14",
        "QtQuick.Controls 2.14",
        "QtQuick.Controls.Fusion 2.14",
        "QtQuick.Controls.Fusion.impl 2.14",
        "QtQuick.Controls.Imagine 2.14",
        "QtQuick.Controls.Imagine.impl 2.14",
        "QtQuick.Controls.Material 2.14",
        "QtQuick.Controls.Material.impl 2.14",
        "QtQuick.Controls.Universal 2.14",
        "QtQuick.Controls.Universal.impl 2.12",
        "QtQuick.Controls.impl 2.14",
        "QtQuick.Shapes 1.14",
        "QtQuick.Templates 2.14",
        "QtQuick.Window 2.2"
    ]
    Component {
        name: "QQuickPdfDocument"
        prototype: "QObject"
        exports: ["QtQuick.Pdf/PdfDocument 5.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QUrl" }
        Property { name: "pageCount"; type: "int"; isReadonly: true }
        Property { name: "password"; type: "string" }
        Property { name: "status"; type: "QPdfDocument::Status"; isReadonly: true }
        Property { name: "title"; type: "string"; isReadonly: true }
        Property { name: "subject"; type: "string"; isReadonly: true }
        Property { name: "author"; type: "string"; isReadonly: true }
        Property { name: "keywords"; type: "string"; isReadonly: true }
        Property { name: "producer"; type: "string"; isReadonly: true }
        Property { name: "creator"; type: "string"; isReadonly: true }
        Property { name: "creationDate"; type: "QDateTime"; isReadonly: true }
        Property { name: "modificationDate"; type: "QDateTime"; isReadonly: true }
        Signal { name: "passwordRequired" }
        Signal { name: "metaDataLoaded" }
        Method {
            name: "pagePointSize"
            type: "QSizeF"
            Parameter { name: "page"; type: "int" }
        }
    }
}
