import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick3D.Helpers 1.15'

Module {
    dependencies: [
        "QtQuick 2.15",
        "QtQuick.Window 2.1",
        "QtQuick3D 1.15",
        "QtQuick3D.Effects 1.15",
        "QtQuick3D.Materials 1.15"
    ]
    Component {
        name: "GridGeometry"
        defaultProperty: "data"
        prototype: "QQuick3DGeometry"
        exports: ["QtQuick3D.Helpers/GridGeometry 1.14"]
        exportMetaObjectRevisions: [0]
        Property { name: "horizontalLines"; type: "int" }
        Property { name: "verticalLines"; type: "int" }
        Property { name: "horizontalStep"; type: "float" }
        Property { name: "verticalStep"; type: "float" }
        Method {
            name: "setHorizontalLines"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setVerticalLines"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setHorizontalStep"
            Parameter { name: "step"; type: "float" }
        }
        Method {
            name: "setVerticalStep"
            Parameter { name: "step"; type: "float" }
        }
    }
    Component {
        name: "PointerPlane"
        defaultProperty: "data"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Helpers/PointerPlane 1.14"]
        exportMetaObjectRevisions: [0]
        Method {
            name: "getIntersectPos"
            type: "QVector3D"
            Parameter { name: "rayPos0"; type: "QVector3D" }
            Parameter { name: "rayPos1"; type: "QVector3D" }
            Parameter { name: "planePos"; type: "QVector3D" }
            Parameter { name: "planeNormal"; type: "QVector3D" }
        }
        Method {
            name: "getIntersectPosFromSceneRay"
            type: "QVector3D"
            Parameter { name: "rayPos0"; type: "QVector3D" }
            Parameter { name: "rayPos1"; type: "QVector3D" }
        }
        Method {
            name: "getIntersectPosFromView"
            type: "QVector3D"
            Parameter { name: "view"; type: "QQuick3DViewport"; isPointer: true }
            Parameter { name: "posInView"; type: "QPointF" }
        }
    }
}
