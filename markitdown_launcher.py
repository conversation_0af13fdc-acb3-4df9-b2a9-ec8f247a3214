#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Markitdown工具集 - 图形化统一启动器
直接显示所有功能模块的图形界面
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import importlib.util

class MarkitdownLauncher:
    """Markitdown工具集图形化启动器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Markitdown工具集 v2.0 - 统一启动器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置窗口居中
        self.center_window()
        
        # 初始化状态
        self.status_var = tk.StringVar(value="就绪")
        self.modules_status = {}
        
        # 创建界面
        self.create_widgets()
        
        # 检查模块状态
        self.check_modules_status()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题区域
        self.create_header(main_frame)
        
        # 功能模块区域
        self.create_modules_section(main_frame)
        
        # 工具区域
        self.create_tools_section(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 主标题
        title_label = ttk.Label(
            header_frame, 
            text="Markitdown工具集 v2.0", 
            font=("Arial", 18, "bold")
        )
        title_label.pack()
        
        # 副标题
        subtitle_label = ttk.Label(
            header_frame, 
            text="多格式文档转换 • AI增强处理 • 专业PDF处理", 
            font=("Arial", 10)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 分隔线
        separator = ttk.Separator(header_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(10, 0))
    
    def create_modules_section(self, parent):
        """创建功能模块区域"""
        modules_frame = ttk.LabelFrame(parent, text="核心功能模块", padding="15")
        modules_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建网格布局
        modules_frame.grid_columnconfigure(0, weight=1)
        modules_frame.grid_columnconfigure(1, weight=1)
        
        # 主转换器
        self.create_module_card(
            modules_frame, 
            row=0, col=0,
            title="📄 Markitdown转换器",
            description="多格式文档转换为Markdown\n支持PDF、Word、Excel、PowerPoint等",
            features=["• 集成Open-PDF2MD增强功能", "• AI智能结构分析", "• 高质量格式保持"],
            command=self.launch_markitdown_converter,
            module_file="markitdown_converter.py"
        )
        
        # PDF转Word工具
        self.create_module_card(
            modules_frame, 
            row=0, col=1,
            title="📝 PDF转Word工具",
            description="专业的PDF转Word转换\n多引擎支持，保持原始格式",
            features=["• 多种转换引擎", "• AI结构分析", "• 图像模式备份"],
            command=self.launch_pdf_to_word,
            module_file="pdf_to_word_gui.py"
        )
        
        # 断句修复工具
        self.create_module_card(
            modules_frame, 
            row=1, col=0,
            title="🔧 断句修复工具",
            description="AI驱动的Markdown断句修复\n提升文档可读性和格式",
            features=["• AI智能修复", "• 多种修复策略", "• 本地Ollama支持"],
            command=self.launch_sentence_fixer,
            module_file="fix_markdown_sentences.py"
        )
        
        # 增强功能测试
        self.create_module_card(
            modules_frame, 
            row=1, col=1,
            title="🧪 功能测试中心",
            description="验证所有增强功能状态\n诊断问题，确保正常运行",
            features=["• 全面功能检测", "• 依赖状态检查", "• 详细测试报告"],
            command=self.launch_feature_test,
            module_file="test_enhanced_features.py"
        )
    
    def create_tools_section(self, parent):
        """创建工具区域"""
        tools_frame = ttk.LabelFrame(parent, text="系统工具", padding="15")
        tools_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 工具按钮框架
        buttons_frame = ttk.Frame(tools_frame)
        buttons_frame.pack(fill=tk.X)
        
        # 依赖管理按钮
        deps_btn = ttk.Button(
            buttons_frame,
            text="🔧 安装/更新依赖",
            command=self.install_dependencies,
            width=20
        )
        deps_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新状态按钮
        refresh_btn = ttk.Button(
            buttons_frame,
            text="🔄 刷新状态",
            command=self.refresh_status,
            width=15
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开项目目录按钮
        folder_btn = ttk.Button(
            buttons_frame,
            text="📁 打开项目目录",
            command=self.open_project_folder,
            width=15
        )
        folder_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 帮助按钮
        help_btn = ttk.Button(
            buttons_frame,
            text="❓ 帮助文档",
            command=self.show_help,
            width=12
        )
        help_btn.pack(side=tk.LEFT)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 状态标签
        status_label = ttk.Label(status_frame, text="状态:")
        status_label.pack(side=tk.LEFT)
        
        status_value = ttk.Label(status_frame, textvariable=self.status_var)
        status_value.pack(side=tk.LEFT, padx=(5, 0))
        
        # 版本信息
        version_label = ttk.Label(status_frame, text="v2.0 Enhanced", font=("Arial", 8))
        version_label.pack(side=tk.RIGHT)
    
    def create_module_card(self, parent, row, col, title, description, features, command, module_file):
        """创建功能模块卡片"""
        # 主卡片框架
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, sticky="nsew", padx=5, pady=5)
        
        # 描述
        desc_label = ttk.Label(
            card_frame, 
            text=description, 
            font=("Arial", 9),
            justify=tk.LEFT
        )
        desc_label.pack(anchor="w", pady=(0, 8))
        
        # 特性列表
        features_text = "\n".join(features)
        features_label = ttk.Label(
            card_frame, 
            text=features_text, 
            font=("Arial", 8),
            foreground="gray",
            justify=tk.LEFT
        )
        features_label.pack(anchor="w", pady=(0, 10))
        
        # 按钮和状态框架
        button_frame = ttk.Frame(card_frame)
        button_frame.pack(fill=tk.X)
        
        # 启动按钮
        launch_btn = ttk.Button(
            button_frame,
            text="启动",
            command=command,
            style="Accent.TButton"
        )
        launch_btn.pack(side=tk.LEFT)
        
        # 状态指示器
        status_indicator = ttk.Label(button_frame, text="●", font=("Arial", 12))
        status_indicator.pack(side=tk.RIGHT)
        
        # 保存状态指示器引用
        self.modules_status[module_file] = status_indicator
    
    def check_modules_status(self):
        """检查模块状态"""
        def check_in_thread():
            for module_file, indicator in self.modules_status.items():
                try:
                    if os.path.exists(module_file):
                        # 尝试导入模块检查语法
                        spec = importlib.util.spec_from_file_location("module", module_file)
                        if spec:
                            self.root.after(0, lambda i=indicator: self.update_status_indicator(i, "green"))
                        else:
                            self.root.after(0, lambda i=indicator: self.update_status_indicator(i, "orange"))
                    else:
                        self.root.after(0, lambda i=indicator: self.update_status_indicator(i, "red"))
                except Exception:
                    self.root.after(0, lambda i=indicator: self.update_status_indicator(i, "orange"))
        
        threading.Thread(target=check_in_thread, daemon=True).start()
    
    def update_status_indicator(self, indicator, color):
        """更新状态指示器"""
        indicator.configure(foreground=color)
    
    def update_status(self, message):
        """更新状态信息"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def launch_markitdown_converter(self):
        """启动Markitdown转换器"""
        self.launch_module("markitdown_converter.py", "Markitdown转换器")
    
    def launch_pdf_to_word(self):
        """启动PDF转Word工具"""
        self.launch_module("pdf_to_word_gui.py", "PDF转Word工具")
    
    def launch_sentence_fixer(self):
        """启动断句修复工具"""
        self.launch_module("fix_markdown_sentences.py", "断句修复工具")
    
    def launch_feature_test(self):
        """启动功能测试"""
        self.launch_module("test_enhanced_features.py", "功能测试中心")
    
    def launch_module(self, module_file, module_name):
        """启动指定模块"""
        if not os.path.exists(module_file):
            messagebox.showerror("错误", f"未找到模块文件: {module_file}")
            return
        
        try:
            self.update_status(f"正在启动{module_name}...")
            
            # 在新进程中启动模块
            subprocess.Popen([sys.executable, module_file])
            
            self.update_status(f"{module_name}已启动")
            
        except Exception as e:
            messagebox.showerror("启动失败", f"启动{module_name}时出错:\n{str(e)}")
            self.update_status("就绪")
    
    def install_dependencies(self):
        """安装/更新依赖"""
        def install_in_thread():
            try:
                self.root.after(0, lambda: self.update_status("正在安装/更新依赖..."))
                
                if os.path.exists("install_enhanced_dependencies.py"):
                    result = subprocess.run([sys.executable, "install_enhanced_dependencies.py"], 
                                          capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        self.root.after(0, lambda: self.update_status("依赖安装完成"))
                        self.root.after(0, lambda: messagebox.showinfo("成功", "依赖安装/更新完成！"))
                    else:
                        self.root.after(0, lambda: self.update_status("依赖安装失败"))
                        self.root.after(0, lambda: messagebox.showerror("失败", f"依赖安装失败:\n{result.stderr}"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("错误", "未找到依赖安装脚本"))
                    
                # 刷新模块状态
                self.root.after(0, self.check_modules_status)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"安装过程出错:\n{str(e)}"))
                self.root.after(0, lambda: self.update_status("就绪"))
        
        threading.Thread(target=install_in_thread, daemon=True).start()
    
    def refresh_status(self):
        """刷新状态"""
        self.update_status("正在刷新状态...")
        self.check_modules_status()
        self.update_status("状态已刷新")
    
    def open_project_folder(self):
        """打开项目目录"""
        try:
            if sys.platform == "darwin":  # macOS
                subprocess.run(["open", "."])
            elif sys.platform == "win32":  # Windows
                subprocess.run(["explorer", "."])
            else:  # Linux
                subprocess.run(["xdg-open", "."])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开项目目录:\n{str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
Markitdown工具集 v2.0 使用说明

🎯 核心功能模块:
• Markitdown转换器: 多格式文档转换为Markdown
• PDF转Word工具: 专业的PDF转Word转换
• 断句修复工具: AI驱动的Markdown断句修复
• 功能测试中心: 验证所有功能状态

🔧 系统工具:
• 安装/更新依赖: 自动管理所需的依赖库
• 刷新状态: 更新模块状态显示
• 打开项目目录: 快速访问项目文件

📊 状态指示器:
• 绿色●: 模块正常可用
• 橙色●: 模块存在但可能有问题
• 红色●: 模块文件缺失

📚 更多帮助:
• 查看 快速开始.md
• 查看 ENHANCED_FEATURES.md
• 查看 项目结构说明.md
        """
        
        messagebox.showinfo("帮助", help_text)
    
    def run(self):
        """运行启动器"""
        try:
            # 设置样式
            style = ttk.Style()
            try:
                style.configure("Accent.TButton", font=("Arial", 9, "bold"))
            except:
                pass
            
            # 启动主循环
            self.root.mainloop()
            
        except KeyboardInterrupt:
            pass
        except Exception as e:
            messagebox.showerror("错误", f"启动器运行出错:\n{str(e)}")


def main():
    """主函数"""
    try:
        launcher = MarkitdownLauncher()
        launcher.run()
    except Exception as e:
        print(f"启动器初始化失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
