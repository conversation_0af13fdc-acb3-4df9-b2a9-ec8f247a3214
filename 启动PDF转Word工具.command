#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 检查是否有Miniconda环境
if [ -f ~/miniconda3/bin/activate ] || [ -f ~/opt/miniconda3/bin/activate ] || [ -f /opt/homebrew/Caskroom/miniconda/base/bin/activate ]; then
    echo "使用Miniconda环境..."

    # 尝试不同的Miniconda路径
    if [ -f ~/miniconda3/bin/activate ]; then
        source ~/miniconda3/bin/activate
    elif [ -f ~/opt/miniconda3/bin/activate ]; then
        source ~/opt/miniconda3/bin/activate
    elif [ -f /opt/homebrew/Caskroom/miniconda/base/bin/activate ]; then
        source /opt/homebrew/Caskroom/miniconda/base/bin/activate
    fi

    # 检查是否已安装必要的库
    if ! python -c "import pdf2docx" 2>/dev/null; then
        echo "正在安装pdf2docx库..."
        pip install pdf2docx
    fi

    if ! python -c "import docx" 2>/dev/null; then
        echo "正在安装python-docx库..."
        pip install python-docx
    fi

    if ! python -c "import pytesseract" 2>/dev/null; then
        echo "正在安装OCR相关库..."
        pip install pytesseract pdf2image PyPDF2 pillow
    fi

    if ! python -c "import fitz" 2>/dev/null; then
        echo "正在安装PyMuPDF库..."
        pip install PyMuPDF
    fi

    # 检查AI分析器依赖
    if ! python -c "import requests" 2>/dev/null; then
        echo "正在安装requests库..."
        pip install requests
    fi

    # 检查增强功能依赖
    if ! python -c "import pdfplumber" 2>/dev/null; then
        echo "检测到缺少增强功能依赖，正在安装..."
        python install_enhanced_dependencies.py
    fi

    # 检查是否已安装OCRmyPDF
    if ! command -v ocrmypdf &> /dev/null; then
        echo "OCRmyPDF未安装，尝试安装..."
        if [ "$(uname)" == "Darwin" ]; then
            # macOS
            if command -v brew &> /dev/null; then
                brew install ocrmypdf
            else
                echo "请安装Homebrew后再安装OCRmyPDF: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                echo "然后运行: brew install ocrmypdf"
            fi
        elif [ "$(expr substr $(uname -s) 1 5)" == "Linux" ]; then
            # Linux
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y ocrmypdf
            elif command -v yum &> /dev/null; then
                sudo yum install -y ocrmypdf
            else
                echo "请手动安装OCRmyPDF: https://ocrmypdf.readthedocs.io/en/latest/installation.html"
            fi
        else
            echo "请手动安装OCRmyPDF: https://ocrmypdf.readthedocs.io/en/latest/installation.html"
        fi
    fi

    # 启动应用程序
    python pdf_to_word_gui.py
else
    echo "未找到Miniconda环境，使用系统Python..."

    # 检查是否已安装必要的库
    if ! python3 -c "import pdf2docx" 2>/dev/null; then
        echo "正在安装pdf2docx库..."
        pip3 install pdf2docx
    fi

    if ! python3 -c "import docx" 2>/dev/null; then
        echo "正在安装python-docx库..."
        pip3 install python-docx
    fi

    if ! python3 -c "import pytesseract" 2>/dev/null; then
        echo "正在安装OCR相关库..."
        pip3 install pytesseract pdf2image PyPDF2 pillow
    fi

    if ! python3 -c "import fitz" 2>/dev/null; then
        echo "正在安装PyMuPDF库..."
        pip3 install PyMuPDF
    fi

    # 检查AI分析器依赖
    if ! python3 -c "import requests" 2>/dev/null; then
        echo "正在安装requests库..."
        pip3 install requests
    fi

    # 检查增强功能依赖
    if ! python3 -c "import pdfplumber" 2>/dev/null; then
        echo "检测到缺少增强功能依赖，正在安装..."
        python3 install_enhanced_dependencies.py
    fi

    # 检查是否已安装OCRmyPDF
    if ! command -v ocrmypdf &> /dev/null; then
        echo "OCRmyPDF未安装，尝试安装..."
        if [ "$(uname)" == "Darwin" ]; then
            # macOS
            if command -v brew &> /dev/null; then
                brew install ocrmypdf
            else
                echo "请安装Homebrew后再安装OCRmyPDF: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                echo "然后运行: brew install ocrmypdf"
            fi
        elif [ "$(expr substr $(uname -s) 1 5)" == "Linux" ]; then
            # Linux
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y ocrmypdf
            elif command -v yum &> /dev/null; then
                sudo yum install -y ocrmypdf
            else
                echo "请手动安装OCRmyPDF: https://ocrmypdf.readthedocs.io/en/latest/installation.html"
            fi
        else
            echo "请手动安装OCRmyPDF: https://ocrmypdf.readthedocs.io/en/latest/installation.html"
        fi
    fi

    # 启动应用程序
    python3 pdf_to_word_gui.py
fi
